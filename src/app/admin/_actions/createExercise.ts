'use server'

import { assertRole } from '@/libs/permissions/rbac'

/**
 * Example server action that requires 'admin' role.
 * Throws if the user is not an admin.
 */
export async function createExerciseAction(_formData: FormData) {
  void _formData // silence ESLint unused warning
  // Enforce RBAC: only admins can create exercises
  await assertRole(['admin'])

  // ...actual creation logic would go here
  // For demonstration, just return a success object
  return { success: true, message: 'Exercise created (dummy action)' }
}
