'use server'

import { getSupabaseRouteHandlerClient } from '@/libs/supabase/server'
import { revalidatePath } from 'next/cache'
import { cookies } from 'next/headers'
import { z } from 'zod/v4'
import type { ExerciseFormData } from './ExerciseForm' // Assuming schema is co-located or imported

/**
 * Verifies that the current user is authenticated and has the 'admin' role.
 * Checks role from the database for consistent security.
 * Throws an error if the user is not authenticated or not an admin.
 * @returns {Promise<ReturnType<typeof getSupabaseRouteHandlerClient>>} The Supabase client instance for an authenticated admin.
 * @throws {Error} If authentication, database query, or authorization fails.
 */
async function checkAdminRole() {
  const cookieStore = cookies()
  // @ts-expect-error - Acknowledging potential type issue from previous steps
  const supabase = getSupabaseRouteHandlerClient(cookieStore)
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser()

  if (userError || !user) {
    throw new Error('Unauthorized: User not authenticated.')
  }

  // Check role from database for consistent security
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', user.id)
    .single()

  if (profileError || !profile) {
    console.error('Profile fetch error:', profileError)
    throw new Error('Unauthorized: Unable to verify user role.')
  }

  if (profile.role !== 'admin') {
    throw new Error('Unauthorized: Admin role required.')
  }
  return supabase // Return client for reuse in the action
}

/**
 * Zod schema for validating exercise form data on the server-side.
 * Includes refine checks for JSON structure.
 */
const exerciseSchema = z.object({
  id: z.string().optional(), // Include ID for update validation, but exclude from insert/update payload usually
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  video_tutorial_url: z
    .string()
    .url('Must be a valid URL')
    .optional()
    .default(''),
  equipment_required: z.string().optional().default(''), // Raw comma-separated string from form
  evaluation_criteria: z.string().min(1, 'Evaluation criteria are required'),
  medal_thresholds: z
    .string()
    .min(1, 'Medal thresholds are required')
    .refine(
      (val) => {
        try {
          const parsed = JSON.parse(val)
          // Basic structure check (expecting an object)
          return typeof parsed === 'object' && parsed !== null
        } catch (_e) {
          return false
        }
      },
      { message: 'Must be valid JSON' },
    ),
})

/**
 * Type for the data structure returned by server actions.
 */
type ActionResponse =
  | { success: true }
  | { success: false; error: string; issues?: object }

// --- Helper: Prepare Data for DB --- //

/**
 * Prepares exercise form data for insertion/update into the database.
 * Parses comma-separated equipment string into an array and JSON string thresholds into an object.
 * Assumes DB expects `equipment_required` as `text[]` and `medal_thresholds` as `jsonb`.
 * @param {ExerciseFormData} formData - Validated form data.
 * @returns {object} Data ready for Supabase insert/update (excluding ID).
 * @throws {Error} If medal thresholds JSON parsing fails unexpectedly.
 */
function prepareExerciseDataForDb(formData: ExerciseFormData): Omit<
  ExerciseFormData,
  'id' | 'equipment_required' | 'medal_thresholds'
> & {
  equipment_required: string[] // Expecting DB to store as array
  medal_thresholds: Record<string, unknown> // Expecting DB to store as JSONB
} {
  // Parse equipment string into array, trim whitespace, remove empty strings
  const equipmentArray = (formData.equipment_required || '')
    .split(',')
    .map((item) => item.trim())
    .filter((item) => item !== '')

  // Parse medal thresholds JSON string
  let parsedThresholds: Record<string, unknown> = {}
  try {
    parsedThresholds = JSON.parse(formData.medal_thresholds)
  } catch (e) {
    console.error(
      'Unexpected error parsing medal thresholds in prepare function:',
      e,
    )
    throw new Error('Invalid medal thresholds JSON format during preparation.')
  }

  const { id, equipment_required, medal_thresholds, ...rest } = formData

  return {
    ...rest,
    equipment_required: equipmentArray,
    medal_thresholds: parsedThresholds,
  }
}

// --- Server Actions --- //

/**
 * Server Action: Creates a new exercise in the database.
 * Requires admin privileges.
 * @param {ExerciseFormData} formData - Data submitted from the exercise form.
 * @returns {Promise<ActionResponse>} Object indicating success or failure with errors/issues.
 */
export async function createExercise(
  formData: ExerciseFormData,
): Promise<ActionResponse> {
  try {
    const supabase = await checkAdminRole()

    // Validate incoming form data
    const validationResult = exerciseSchema.safeParse(formData)
    if (!validationResult.success) {
      console.error(
        'Create Validation Error:',
        z.treeifyError(validationResult.error),
      )
      return {
        success: false,
        error: 'Invalid exercise data.',
        issues: z.treeifyError(validationResult.error),
      }
    }

    // Prepare data for the database (parsing JSON, arrays, etc.)
    const dbData = prepareExerciseDataForDb(validationResult.data)

    // Insert into Supabase
    const { error } = await supabase.from('exercises').insert(dbData)

    if (error) {
      console.error('Supabase Create Error:', error.message)
      return { success: false, error: `Database error: ${error.message}` }
    }

    revalidatePath('/admin/exercises')
    return { success: true }
  } catch (error: unknown) {
    const message =
      error instanceof Error ? error.message : 'An unexpected error occurred.'
    console.error('Create Exercise Action Failed:', message)
    return { success: false, error: message }
  }
}

/**
 * Server Action: Updates an existing exercise in the database.
 * Requires admin privileges.
 * @param {string} exerciseId - The ID of the exercise to update.
 * @param {ExerciseFormData} formData - Data submitted from the exercise form.
 * @returns {Promise<ActionResponse>} Object indicating success or failure with errors/issues.
 */
export async function updateExercise(
  exerciseId: string,
  formData: ExerciseFormData,
): Promise<ActionResponse> {
  if (!exerciseId) {
    return { success: false, error: 'Exercise ID is required for update.' }
  }

  try {
    const supabase = await checkAdminRole()

    // Validate incoming form data (make sure ID matches if present)
    const validationResult = exerciseSchema.safeParse({
      ...formData,
      id: exerciseId,
    })
    if (!validationResult.success) {
      console.error(
        'Update Validation Error:',
        z.treeifyError(validationResult.error),
      )
      return {
        success: false,
        error: 'Invalid exercise data.',
        issues: z.treeifyError(validationResult.error),
      }
    }

    // Prepare data for the database (parsing JSON, arrays, etc.)
    // Exclude 'id' from the data payload sent to Supabase update
    const { id, ...validatedData } = validationResult.data
    const dbData = prepareExerciseDataForDb(validatedData as ExerciseFormData) // Cast needed after omitting id

    // Update in Supabase
    const { error } = await supabase
      .from('exercises')
      .update(dbData)
      .eq('id', exerciseId)

    if (error) {
      console.error('Supabase Update Error:', error.message)
      return { success: false, error: `Database error: ${error.message}` }
    }

    revalidatePath('/admin/exercises')
    return { success: true }
  } catch (error: unknown) {
    const message =
      error instanceof Error ? error.message : 'An unexpected error occurred.'
    console.error('Update Exercise Action Failed:', message)
    return { success: false, error: message }
  }
}

/**
 * Server Action: Deletes an exercise from the database.
 * Requires admin privileges.
 * @param {string} exerciseId - The ID of the exercise to delete.
 * @returns {Promise<ActionResponse>} Object indicating success or failure.
 */
export async function deleteExercise(
  exerciseId: string,
): Promise<ActionResponse> {
  if (!exerciseId) {
    return { success: false, error: 'Exercise ID is required for deletion.' }
  }
  try {
    const supabase = await checkAdminRole()

    // Delete from Supabase
    const { error } = await supabase
      .from('exercises')
      .delete()
      .eq('id', exerciseId)

    if (error) {
      console.error('Supabase Delete Error:', error.message)
      return { success: false, error: `Database error: ${error.message}` }
    }

    revalidatePath('/admin/exercises')
    return { success: true }
  } catch (error: unknown) {
    const message =
      error instanceof Error ? error.message : 'An unexpected error occurred.'
    console.error('Delete Exercise Action Failed:', message)
    return { success: false, error: message }
  }
}
