'use server'

import { getSupabaseRouteHandlerClient } from '@/libs/supabase'
import { revalidatePath } from 'next/cache'
import { cookies } from 'next/headers'
import type { z } from 'zod/v4'
import { EditProfileSchema } from './profileSchema'

/**
 * Server action to update the user's profile.
 * - Validates input
 * - Checks username uniqueness if changed
 * - Updates allowed fields
 * - Revalidates relevant paths
 * - Returns { error?: string }
 */
export async function updateProfile(form: z.infer<typeof EditProfileSchema>) {
  // Validate input
  const parsed = EditProfileSchema.safeParse(form)
  if (!parsed.success) {
    return {
      error: parsed.error.issues
        .map((e: { message: string }) => e.message)
        .join(', '),
    }
  }
  const values = parsed.data

  // Get authenticated user
  const cookieStore = await cookies()
  const supabase = getSupabaseRouteHandlerClient(cookieStore)
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return { error: 'Not authenticated' }
  }

  // Fetch current profile
  const { data: currentProfile, error: profileError } = await supabase
    .from('profiles')
    .select('username')
    .eq('id', user.id)
    .single()

  if (profileError || !currentProfile) {
    return { error: 'Profile not found' }
  }

  // If username changed, check uniqueness
  if (values.username !== currentProfile.username) {
    const { data: existing } = await supabase
      .from('profiles')
      .select('id')
      .eq('username', values.username)
      .neq('id', user.id)
      .maybeSingle()
    if (existing) {
      return { error: 'Username already taken' }
    }
  }

  // Prepare update payload
  interface UpdateProfilePayload {
    full_name: string
    username: string
    country: string
    gender: 'male' | 'female' | 'other'
    weight_category: 'under_95kg' | 'over_95kg'
    avatar_url?: string
    titles: string[]
    social_links: Record<string, string>
  }

  const updatePayload: UpdateProfilePayload = {
    full_name: values.full_name,
    username: values.username,
    country: values.country,
    gender: values.gender,
    weight_category: values.weight_category,
    avatar_url: values.avatar_url,
    titles: values.titles
      ? values.titles
          .split(',')
          .map((t) => t.trim())
          .filter(Boolean)
      : [],
    social_links: values.social_links
      ? (() => {
          try {
            return JSON.parse(values.social_links)
          } catch {
            return {}
          }
        })()
      : {},
  }

  // Update profile
  const { error: updateError } = await supabase
    .from('profiles')
    .update(updatePayload)
    .eq('id', user.id)

  if (updateError) {
    return { error: updateError.message }
  }

  // Revalidate relevant paths
  revalidatePath('/account')
  revalidatePath(`/profile/${values.username}`)

  return { error: null }
}
