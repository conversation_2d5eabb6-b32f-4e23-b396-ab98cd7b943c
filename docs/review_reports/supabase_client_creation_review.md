### **Phase 1: Contextual Exploration & Analysis**

#### **Context Summary**

*   **Tools Utilized & Key Discoveries:**
    *   **`list_dir`** was used on `src/libs/supabase` to understand the file structure for client creation.
    *   **`grep_search`** for `createServerClient` and `@/libs/supabase` was used to identify all files using various Supabase client initialization patterns.
    *   **`read_file`** was used to inspect key files:
        *   `src/libs/supabase/index.ts`: Revealed that the exported `createServerClient` is an alias for the `createClient` function from `src/libs/supabase/server.ts`.
        *   `src/libs/supabase/server.ts`: Confirmed that `createClient` (aliased as `createServerClient`) intentionally uses a "dummy" cookie handler (`getAll() { return [] }`). This makes it a **read-only client suitable for Server Components but critically unsafe for any operations involving authentication state** (like checking permissions or modifying user data).
        *   `src/libs/auth/serverPermissions.ts` & `src/libs/auth/protectedApi.ts`: Confirmed these files incorrectly use the read-only client for security-sensitive authentication checks, which is a significant bug.
        *   `src/app/auth/actions.ts`: Showcased the **correct pattern** for Server Actions, using `@supabase/ssr`'s `createServerClient` directly with cookie handling provided by `next/headers`. This is the reference implementation to follow.
        *   `src/app/api/exercises/route.ts`: Showcased the **correct pattern** for API Routes using the `getSupabaseRouteHandlerClient` helper, which correctly handles cookies from the request.

*   **Confirmation of User's Problem Statement:** The analysis confirms the user's report. The application has a critical inconsistency where a read-only Supabase client (without cookie support) is being used in security-sensitive contexts (`serverPermissions.ts`, `protectedApi.ts`), leading to unreliable and incorrect authentication state management.

*   **Key Files, Functions, Types & Structures Involved:**
    *   **Files with incorrect implementation:** `src/libs/auth/serverPermissions.ts`, `src/libs/auth/protectedApi.ts`.
    *   **File with inconsistent (but not buggy) implementation:** `src/app/exercises/page.tsx`.
    *   **Files with correct reference implementations:** `src/app/auth/actions.ts` (for Server Actions), `src/app/api/exercises/route.ts` (for API Routes/Route Handlers).
    *   **Core helper functions to be refactored:** `getCurrentUser()`, `getApiUser()`.
    *   **Core helper functions to use:** `getSupabaseRouteHandlerClient()`.

*   **Current Data Flow & Observed Patterns:** The primary anti-pattern is the import and direct invocation of `createServerClient` from `@/libs/supabase` for tasks that require an authenticated session context. The correct pattern involves creating a client scoped to a specific request or action, providing it with the `cookieStore` from `next/headers`.

*   **Potential Challenges, Risks & Considerations:**
    *   The refactor will require making several synchronous functions in `serverPermissions.ts` asynchronous because `cookies()` from `next/headers` is an async function.
    *   Tests for these modules will need to be updated to correctly mock the `next/headers` cookie store.
    *   Any code calling the functions in `serverPermissions.ts` will need to be updated to `await` the results.

### **Phase 2: Formulate a Plan**

Here is the detailed, step-by-step plan to remediate the identified issues and standardize Supabase client usage across the application.

#### **Stage 1: Centralize Supabase Client Creation for Server Actions & Permissions**

*   **What:** Create a single, reusable function that correctly instantiates a Supabase server client for use in Server Actions and server-side permission checks. This function will encapsulate the logic of retrieving credentials and setting up the cookie handler from `next/headers`.
*   **Where:** Add a new exported function named `createServerActionClient` to the `src/libs/supabase/server.ts` file. This function will be based on the implementation found in `src/app/auth/actions.ts`.
*   **Why:** This adheres to the DRY principle, eliminating code duplication and ensuring that all server-side logic that reads or modifies authentication state uses one consistent, correct, and easily maintainable method for client creation.

──────────

#### **Stage 2: Refactor `serverPermissions.ts` to Use the New Action Client**

*   **What:** Modify all functions within `src/libs/auth/serverPermissions.ts` (e.g., `getCurrentUser`, `requirePermission`, `requireRole`) to use the new `createServerActionClient` from Stage 1. This will involve making the functions that use it `async`.
*   **Where:** Refactor the file `src/libs/auth/serverPermissions.ts`. The direct import of `createServerClient` will be removed, and `getCurrentUser` will be updated to `await` the new client creation function. Subsequent functions in the file that depend on `getCurrentUser` will also need the `async` keyword.
*   **Why:** This is the most critical step to fix the core authentication bug. It ensures that permission checks are performed against the user's actual, current session state as read from the cookies, rather than an unauthenticated or stale state.

──────────

#### **Stage 3: Refactor `protectedApi.ts` to Use the Correct Route Handler Client**

*   **What:** Update the API protection logic to use the existing `getSupabaseRouteHandlerClient` helper, which is purpose-built for API Routes and Route Handlers. The `getApiUser` function will be refactored to create the client from the incoming `NextRequest` object.
*   **Where:** Refactor the file `src/libs/auth/protectedApi.ts`. The incorrect import of `createServerClient` will be replaced. The `getApiUser` function's signature will remain `(request: NextRequest)`, but its implementation will change to instantiate the client using `getSupabaseRouteHandlerClient(cookies())`.
*   **Why:** This fixes the authentication bug for all protected API routes, ensuring they correctly extract the user's session from the incoming HTTP request's cookies.

──────────

#### **Stage 4: Standardize Read-Only Server Component Client Usage**

*   **What:** Replace the usage of `createServerClient` from `@/libs/supabase` in read-only Server Components with the more explicitly named `createClient` from `@/libs/supabase/server`.
*   **Where:** Update `src/app/exercises/page.tsx` and any other similar Server Components identified.
*   **Why:** While not a functional bug, this change improves code clarity and consistency. It makes it immediately obvious to developers that the client being used is a simple, read-only client, aligning with the project's documentation and preventing future misuse.

──────────

#### **Stage 5: Update Tests and Documentation**

*   **What:**
    1.  Update all relevant unit and integration tests for the modified files. This will primarily involve mocking `next/headers`'s `cookies` function.
    2.  Review and update `src/libs/supabase/README.md` and any other relevant documentation to reflect the new standardized patterns for client creation (i.e., `createServerActionClient` for actions/permissions, `getSupabaseRouteHandlerClient` for routes, and `createClient` for read-only server components).
*   **Where:** `*.test.ts` files associated with `serverPermissions`, `protectedApi`, etc. and any `.md` files documenting data access.
*   **Why:** To ensure the application remains stable and that the new architectural patterns are clearly documented for all team members, preventing a regression to the old, buggy patterns.

Switch to **agent mode** and type **execute** (or **execute stage 1**) to begin.