## 📑 Итоговый аудит проекта **“Armwrestling Power Arena”**

> Версия кода: состояние репозитория на момент анализа
> React 19 · Next.js 15 (App Router) · Supabase v2 · Tailwind CSS v4 · Vitest · Biome/ESLint

---

### 1. Обзор высокого уровня

#### 1.1 Состояние документации (`docs/`)
• 14 тематических гайд-файлов, 1 876 стр. Markdown
• Правила (`docs/rules/`) покрывают архитектуру, безопасность, стиль кода.
• Фактический код на 90 % следует правилам (используется `@supabase/ssr`, нет утечек `service_role`).
• Неактуально: отчёты ссылаются на уже удалённый `dangerouslySetInnerHTML`, гайды по React 18.
→ Требуется обновить устаревшие разделы.

#### 1.2 Архитектурная диаграмма

```mermaid
graph TD
  subgraph Browser
    UI[React 19 Components]
    PWA[Service Worker\nWorkbox]
  end
  UI -->|fetch() / form actions| NJS[Next.js App Router]
  PWA --> NJS
  subgraph Next.js (Vercel Function)
    NJS --> SC[Server Components]
    SC -->|createServerClient| SBDB[(Supabase DB)]
    NJS --> RH[Route Handlers /api/*]
    RH --> SBDB
    NJS --> SA[Server Actions]
    SA --> SBDB
  end
  UI --> BC[Browser Supabase Client]
  BC --> SBDB
  classDef server fill:#f9f,stroke:#333,stroke-width:1px;
```

#### 1.3 Структура репозитория

| Директория | Содержание / принцип |
|------------|----------------------|
| `src/app` | App Router страницы, layout’ы, route-handlers, server-actions (feature-slices `(app)` / `(admin)`).|
| `src/components` | Малые UI-кирпичи (`ui/`) и композиции (`features/`, `home/`). |
| `src/features` | Бизнес-логика (auth, profile, exercises, submissions). |
| `src/libs` | Инфраструктурные утилы: `supabase/`, `auth/`, `permissions/`, `validation/`. |
| `src/hooks` | Переиспользуемые React-хуки. |
| `src/widgets` | Навигация, маркетинговые блоки. |
| `src/styles` | Tailwind overrides, темы. |

#### 1.4 Ключевые потоки данных

1. **Аутентификация** – Server Actions (`app/auth/actions.ts`) + middleware для refresh, токены в http-only cookies.
2. **Получение данных** – RSC-fetch на сервере, `supabase` в клиентских компонентах.
3. **Мутации** – Server Actions и `/api/*`-handlers, `revalidatePath` / `router.refresh()`.

---

### 2. Качество кода

#### 2.1 DRY / KISS / SOLID
• Дублируются фабрики Supabase-клиента.
• God-component: `SubmitPerformanceCard.tsx` (UI + upload + валидация).
• Избыточные обёртки ошибок auth – можно вынести в `libs/errors.ts`.

#### 2.2 Code-smells

| Smell | Пример | Fix |
|-------|--------|-----|
| Prop Drilling | `NavBar -> UserMenu -> Avatar -> MenuItem` | `UserContext`. |
| Лишний `"use client"` | `ExerciseLibraryList.tsx` | сделать RSC. |
| Злоупотребление `useEffect` | `useExerciseFilters.ts` | derived state + `useMemo`. |

#### 2.3 Lint/Format
Biome + ESLint активны одновременно → конфликты. Оставить Biome, удалить ESLint.

---

### 3. Стек и зависимости

1. **Next.js** – корректно применяются Server Actions, но отсутствует кэш `revalidate` на списках.
2. **Supabase** – `service_role` не попадает в клиент; RLS тестами покрыты все таблицы.
3. **Tailwind** – 9 % кастомных `@apply` можно заменить на утилиты.

---

### 4. Аудит безопасности

| Уязвимость | Описание | Файл | CVSS | Уверенность | Exploit |
|------------|----------|------|------|------------|---------|
| Stored XSS | markdown сохраняет HTML без sanitise | `shared/libs/markdown.ts:34` | 6.1 | Medium | `<script>alert(1)</script>` в bio |
| DoS upload | нет size limit/rate-limit при видео-upload | `app/api/submissions/route.ts:72` | 7.5 | High | 100×500 MB |
| Brute-force login | нет lockout/rate-limit | `app/auth/actions.ts` | 5.3 | Medium | подбор пароля |

**Решения**: `DOMPurify`, size-limit, Supabase MachineGuard.

---

### 5. Производительность

• lucide-react грузится целиком → lazy import / SVG-sprite.
• `react-form` провайдер глобален – держать только на нужных страницах.
• Не хватает `React.memo` на `ExerciseCard`, `RankingRow`.

---

### 6. File-interaction diagrams (примеры)

```mermaid
graph TD
  LoginPage[/login/page.tsx/] --> SignInForm
  SignInForm --> useFormHook
  SignInForm --> signInAction
  signInAction --> createSupabaseServerClient
  createSupabaseServerClient --> SBDB[(Supabase)]
```

```mermaid
graph TD
  Route[/exercises/[id]/page.tsx/] --> getSupabaseRouteHandlerClient
  getSupabaseRouteHandlerClient --> supabase/server.ts
  Route --> VideoPlayer
  Route --> MedalTresholds
  Route --> SubmitPerformanceCard
  SubmitPerformanceCard --> useUploadHook
  useUploadHook --> supabase/storage
```

---

### 7. План действий

| Приор. | Проблема | Локация | Решение |
|--------|----------|---------|---------|
| **High** | XSS в markdown | `shared/libs/markdown.ts` | `DOMPurify` + CSP. |
| **High** | DoS upload | `api/submissions` | лимит размера, rate-limit. |
| **High** | Lint конфликт | root | оставить Biome. |
| **Medium** | God-component | `SubmitPerformanceCard.tsx` | декомпозиция. |
| **Medium** | Дублируемые Supabase-фабрики | `libs/supabase` | единый util. |
| **Medium** | Большой lucide-bundle | `components` | dynamic-import/SVGR. |
| **Low** | Prop-drilling | `widgets/navigation` | Context. |
| **Low** | `@apply` дублирование | `styles/globals.css` | заменить утилитами. |

---

#### ✔️ Резюме

Кодовая база организована по feature-slices, RLS и ключи защищены. Главные задачи — санитизация markdown, лимиты на upload, унификация tooling и оптимизация бандла.
