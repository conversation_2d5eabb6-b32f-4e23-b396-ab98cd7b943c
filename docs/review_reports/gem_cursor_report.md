## **Отчет по анализу кодовой базы "Armwrestling Power Arena"**

1. Архитектура и потоки данных
   a. Вся документация в `docs/` покрывает основные аспекты (стек, RLS-политики, тест-план, PWA, стили). Обнаружены расхождения:
      • файлы `react-19-migration.md`, `styling-guide.md` и `arm_pwr_arena_specs.md` не обновлены после перехода на React 19 и Tailwind 4.
      • в `supabase.md` отсутствуют описания последних миграций `20250508*`.
      • в `authentication.md` нет сведений о серверных actions (`src/app/auth/actions.ts`).
      ⇒ Требуется синхронизация.

   b. Диаграмма архитектуры (Mermaid):
   ```mermaid
   graph TD
     subgraph Next.js App Router
       CC[Client Component] -. supabase-js (browser) .-> SB
       SC[Server Component / Server Action] -. ssr-client .-> SB
       RH[Route Handler] -. ssr-client+cookies .-> SB
     end
     SB[Supabase HTTP API] --> DB[(Postgres + Auth + Storage)]
     DB <-- RLS / RPC --> EDGE[Supabase Edge Funcs]
   ```
   c. Структура репозитория
      • `src/app` — маршрут-ориентированный UI (App Router).
      • `src/components` — UI kit + «шородные» компоненты.
      • `src/features` — feature-based (auth, submissions, profile, exercises, theme).
      • `src/libs` — инфраструктура (supabase, auth, permissions, utils, rankings).
      • `src/hooks` — переиспользуемые React-хуки.
      • `src/shared` — кросс-фичевые типы и UI (VideoPlayer).
      • `supabase/` — миграции, функции, RLS.
      • `docs/` — архитектурные и процессные документы.
      Принцип — «feature-slice» + «layered libs».

   d. Ключевые потоки данных
      1. Аутентификация
         • Клиент: `ModernAuthForm` → server-action `signInAction` (`use server`).
         • Action создаёт серверный Supabase-клиент, пишет cookies (SSO).
         • Сессия хранится в http-only cookie, клиентский SDK читает её автоматически. Стратегия едина во всём приложении (middleware, route handler, server action).
      2. Получение данных (пример: страница упражнения)
         • `app/(app)/exercises/[id]/page.tsx` — серверный компонент делает `supabase.from('exercises').select(...)`.
         • Дочерние интерактивные блоки помечены `use client` и получают данные через пропсы или повторный fetch (`useSWR`).
         • Кэш: по-умолчанию `force-cache`; ручная инвалидация через `revalidatePath()` после мутаций.
      3. Изменение данных (пример: отправка видео)
         • Клиентский `SubmissionForm` (client component) вызывает server-action `createSubmission` (в `src/features/submissions/actions.ts`).
         • После insert выполняется `revalidatePath('/submissions')` и `router.refresh()` на клиенте.
         • UI обновляется через серверный ре-рендер.
      4. Оценка GM/Admin
         • `admin-submissions-management-view.tsx` отображает очередь.
         • `evaluationActions.ts` → server action → admin Supabase-клиент (`createAdminClient()`) → update.
         • После update — та же стратегия инвалидации.

   e. Диаграммы файлов (сокращённо)

   **Аутентификация**
   ```mermaid
   graph TD
     A[app/login/page.tsx] --> B[ModernAuthForm.tsx]
     B --> C[app/auth/actions.ts]
     C --> D[supabase.server.ts createServerClient]
     D --> E[Supabase Auth API]
   ```

   **Submissions**
   ```mermaid
   graph TD
     S1[SubmissionForm.tsx] --> SA[features/submissions/actions.ts]
     SA --> SL[libs/validation/submissionSchema.ts]
     SA --> SS[supabase.server.ts]
     SS --> DB[(public.submissions)]
     S1 --> VP[VideoPlayer.tsx]
   ```

   ──────────

2. Качество кода и соответствие принципам
   • DRY:
     – Набор функций `createSupabaseServerClient` дублируется (actions.ts, server.ts, middleware.ts). Вынести в `libs/supabase/factory.ts`.
     – Повторяющиеся Tailwind классы для кнопок/карточек дублируются в `components/Button.tsx` и `components/ui/button.tsx`. Объединить.
   • KISS:
     – `VideoPlayer.tsx` (272 LOC) содержит логику разбора URL, UI-состояния и рендера. Разбить на hook `useVideoMeta` + презентационный компонент.
   • SRP:
     – `features/submissions/overview/*` компоненты смешивают fetch, бизнес-логику, UI. Перенести запросы в server components / hooks.
   • Антипаттерны
     – Prop drilling: `Navbar` → `ProfileLink` → role/username. Предложение — React Context `AuthContext`.
     – Избыточные `"use client"`: `components/ui/card.tsx` (не использует хуки). Убрать директиву.
     – `useEffect` без deps: `useIntersectionObserver.ts` вызывает callback на каждый рендер. Нужно передавать deps-массив.
   • Линтинг / форматирование
     – Biome включён, ESLint — минимальная конфигурация. Конфликты (`biome check` vs `next lint`) не решены. Рекомендуется отключить ESLint или синхронизировать правила (eslint-flat plugin-biome).

   ──────────

3. Стек и зависимости
   • Next.js 15, React 19, App Router. Использование серверных компонентов корректное; Route Handlers применяются только для Supabase callback.
   • Кэширование: `fetch()` без `next: { revalidate }` в большинстве компонентов → полное кэширование, но мутации корректно вызывают `revalidatePath`. Усилить явную декларацию TTL.
   • Supabase:
     – service_role ключ нигде не утекает в клиент. Используется только через `createAdminClient()` (server-only).
     – Все таблицы имеют RLS. Миграции создают helper-функции `is_admin`, `is_grandmaster`.
     – Запросы в lists (`ExerciseLibraryList`) выбирают конкретные поля, N+1 не обнаружено.
   • Tailwind CSS 4: классы используются консистентно; `@apply` почти не применяется.

   ──────────

4. Аудит безопасности

   | Название уязвимости | Описание | Путь к файлу | CVSS (approx) | Уверенность | Шаги для эксплуатации |
   |---------------------|----------|--------------|---------------|-------------|-----------------------|
   | Отсутствие CSRF-защиты для server actions | Формы используют native `<form action={...}>`, но в actions нет верификации CSRF-токена; злоумышленник может отправить POST от имени пользователя | multiple `src/app/**/actions.ts` | 6.5 (Medium) | Средняя | Создать страницу, делающую auto-POST на `/app/auth/actions.ts` с cookies пользователя |
   | No rate-limit на resetPassword | Можно вызывать `resetPasswordAction` неограниченно и слать email-бомбы | `src/app/auth/actions.ts` (L161-195) | 4.3 (Low) | Низкая | Скрипт, вызывающий action в цикле |
   | Missing `Content-Security-Policy` headers | Отсутствует global CSP; XSS-защита полагается только на React | `next.config.ts` | 6.1 | Средняя | Инжектировать `<img src=x onerror=alert(1)>` в текстовые поля, дождаться inline-рендера |
   | Пропуск проверки роли при admin-client | Любой server action, импортирующий `createAdminClient()` без дополнительной проверки, получит полные права | потенциально любое новое *.ts | 7.2 | Высокая | Создать новый server action, использовать admin-client для удаления профиля |

   ──────────

5. Производительность
   • Бандл: `lucide-react` (≈800 icons) тянется целиком; заменить на `next/dynamic(() => import('lucide-react').then(m => m.IconName))` или кастомный tree-shaker.
   • `VideoPlayer` и `modern-auth` формы грузятся на каждой странице — обернуть `next/dynamic` с SSR false.
   • Изображения: `/public/images/**` используются через `<img>`; заменить на `<Image>` с `sizes`/`priority`.
   • Мемоизация: в глубоких списках (`RankingsTable`, `SubmissionHistoryList`) отсутствует `React.memo`; добавить для строк таблиц.

   ──────────

6. Action Plan

   | Приоритет | Проблема | Расположение | Решение |
   |-----------|----------|--------------|---------|
   | High | CSRF-токены для server actions | `src/app/**/actions.ts` | Добавить `csrf()` helper, проверять `cookies["__Host-csrf"]` |
   | High | CSP | `next.config.ts`, `_headers` | Включить `Content-Security-Policy`, `Referrer-Policy`, `X-Frame-Options` |
   | High | Потенциальное злоуп. admin-client | `libs/supabase/auth.ts` + new code | Ограничить export, добавить runtime-assert `if(!isAdminSession) throw` |
   | Medium | Дублирование Supabase-factory | `libs/supabase/**/*.ts` | Создать `factory.ts`, удалить копии |
   | Medium | Убрать лишние `"use client"` | `components/ui/*.tsx` | Перенести в сервер-файлы или оставить client-только интерактив |
   | Medium | Оптимизация `lucide-react` | All pages | Динамический импорт, иконки по штучно |
   | Medium | Обновить Docs | `docs/*.md` | Синхронизировать с кодом, описать React 19, Tailwind 4 |
   | Low | Мемоизация таблиц | `RankingsTable`, `SubmissionHistoryList` | `React.memo`, `useCallback` для cell render |
   | Low | Линтинг единый | `eslint.config.mjs`, `biome.json` | Перейти на flat ESLint + plugin-biome или полностью на Biome |

──────────
