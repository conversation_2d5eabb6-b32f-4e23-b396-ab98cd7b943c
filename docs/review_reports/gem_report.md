## **Отчет по анализу кодовой базы "Armwrestling Power Arena"**

### **1. Обзор высокого уровня: Архитектура и Потоки Данных**

#### **0. Документация проекта**

*   **Папка `docs/`**: Документация в целом полная и хорошо структурированная. Она охватывает ключевые аспекты проекта: архитектуру, аутентификацию, базу данных, PWA и руководство по стилю. Файлы `review_reports` показывают, что в проекте уже проводился code review, что является отличной практикой. Документация актуальна и соответствует текущей реализации.
*   **Папка `docs/rules/`**: Правила для разработчиков и LLM-ассистента четко определены. Они охватывают все, от именования веток и коммитов до правил безопасности и использования технологий (Next.js, Supabase, `shadcn/ui`). Эти правила являются надежной основой для поддержания высокого качества кода. В ходе анализа я убедился, что проект в основном следует этим правилам.

#### **1. Диаграмма Архитектуры**

```mermaid
graph TD
    subgraph "Клиент (Браузер)"
        A[Next.js Frontend: App Router]
    end

    subgraph "Backend as a Service (BaaS)"
        B[Supabase]
        B_Auth[Auth]
        B_DB[PostgreSQL DB]
        B_Storage[Storage]
        B_Functions[Edge Functions]
        B --- B_Auth & B_DB & B_Storage & B_Functions
    end

    subgraph "Взаимодействие"
        C[Server Components]
        D[Client Components]
        E[Server Actions]
        F[Route Handlers]
        G[Middleware]
    end

    A -- "Интерактивность" --> D
    A -- "Рендеринг на сервере" --> C

    C -- "Прямой вызов (RSC)" --> B_DB
    D -- "Вызов клиента Supabase" --> B
    E -- "Изменение данных" --> B_DB
    F -- "API эндпоинты" --> B_DB

    G -- "Управление сессией" --> B_Auth

    style B fill:#3ecf8e,stroke:#333,stroke-width:2px
    style A fill:#000,stroke:#fff,stroke-width:2px,color:#fff
```

#### **2. Описание структуры репозитория**

*   `/src/app`: Ядро приложения, использующее Next.js App Router. Каждая папка соответствует сегменту URL. Используются Route Groups (`(app)`, `(admin)`) для разделения логики и макетов без влияния на URL.
*   `/src/components`: Содержит переиспользуемые React-компоненты. Структурирован по принципу `features` (компоненты для конкретных фич) и `ui` (базовые UI-элементы, в основном из `shadcn/ui`).
*   `/src/libs`: Модули с общей логикой. `supabase/` содержит раздельную логику для создания клиентов на сервере, клиенте и в middleware, что является лучшей практикой. `permissions/` реализует детальную систему RBAC.
*   `/src/hooks`: Кастомные React хуки (`useLocalStorage`, `useIntersectionObserver`, `useRoleAccess`).
*   `/src/styles`: Глобальные стили и конфигурация Tailwind CSS.
*   `/supabase`: Конфигурация и миграции для Supabase, что соответствует подходу "инфраструктура как код".
*   `/docs`: Проектная документация.
*   `/public`: Статические ассеты (изображения, манифест PWA).

**Принципы структуры**: Проект следует принципам **Feature-Sliced Design (FSD)** в легкой форме, разделяя логику на слои (`app`, `components`, `libs`) и фичи (`features/`, `app/(app)/...`). Это способствует хорошей модульности и масштабируемости.

#### **3. Основные потоки данных (Data Flow)**

1.  **Аутентификация пользователя**:
    *   **Механизм**: Используется `@supabase/ssr` для управления сессией через `cookies`. Это консистентно применяется во всем приложении.
    *   **Процесс**:
        1.  Пользователь взаимодействует с формой входа/регистрации (клиентский компонент).
        2.  Вызывается Server Action (`/src/app/auth/actions.ts`), который использует `createServerClient` для взаимодействия с Supabase Auth.
        3.  `src/middleware.ts` на каждый запрос проверяет и обновляет сессию, обеспечивая ее доступность в Server Components и Server Actions.
    *   **Консистентность**: Подход очень консистентен и безопасен, так как сессия управляется на сервере через HTTP-only cookies.

2.  **Получение и отображение данных**:
    *   **Механизм**: Преимущественно используются **Server Components**.
    *   **Процесс**: Страницы (например, `/app/profile/[username]/page.tsx`) являются асинхронными Server Components, которые напрямую вызывают `createServerClient(cookies())` для получения данных из Supabase. Это эффективно, так как данные загружаются на сервере, и клиенту отправляется уже готовый HTML.
    *   **Клиентские компоненты**: Используются для интерактивных элементов (например, фильтры на странице рангов), но данные для них часто подготавливаются на сервере и передаются как пропсы.

3.  **Изменение данных**:
    *   **Механизм**: Используются **Server Actions**.
    *   **Процесс**: Формы (например, редактирование профиля) вызывают Server Actions. Внутри экшена происходит валидация данных (с помощью Zod) и вызов клиента Supabase для обновления БД. После успешного выполнения вызывается `revalidatePath('/')` или `redirect('/')` для обновления UI.
    *   **Пример**: `deleteExercise` в `/src/app/(admin)/admin/exercises/page.tsx` вызывает Server Action, который удаляет запись и возвращает результат. UI обновляется на клиенте на основе этого результата.

#### **4. Детализированная диаграмма связей файлов (Фича: "Администрирование упражнений")**

```mermaid
graph TD
    subgraph "User Interaction"
        A["/app/(admin)/admin/exercises/page.tsx (UI)"]
    end

    subgraph "Client-Side Logic"
        B["/src/features/exercises/ExerciseModalForm.tsx (Форма)"]
    end

    subgraph "Server-Side Logic"
        C["/src/features/exercises/actions.ts (Server Actions)"]
    end

    subgraph "Database Layer"
        D["/src/libs/supabase/server.ts (Supabase Client)"]
        E["Supabase DB (exercises table)"]
    end

    A -- "Открывает модальное окно" --> B
    B -- "Вызывает onSumbit" --> C
    C -- "Использует createServerClient" --> D
    D -- "Выполняет запрос" --> E
```

### **2. Глубокий анализ качества кода и следования принципам**

1.  **Принципы программирования**:
    *   **DRY**: В целом, принцип соблюдается хорошо. Переиспользуемые компоненты вынесены в `src/components/ui`, а логика - в хуки и `libs`.
        *   **Найдено дублирование**: Компоненты `FeaturedExercisesList.tsx` и `ExerciseLibraryList.tsx` содержат почти идентичную логику для `ObservedExerciseCard` и управления состоянием `activeCardId`.
        *   **Рефакторинг**: Эту логику можно вынести в общий компонент-обертку или кастомный хук, который бы принимал список упражнений и возвращал `activeCardId` и обработчики.
    *   **KISS**: Код в основном простой и понятный.
        *   **Усложнение**: Функция `cn` в `src/libs/utils.ts` имеет кастомную логику для обработки `pt-`/`pb-` классов. Хотя это решает специфическую проблему, это усложняет стандартную утилиту и может быть неожиданным для новых разработчиков. Стандартного `twMerge` обычно достаточно.
    *   **SOLID (SRP)**:
        *   **Нарушение SRP**: Компонент `/src/app/(admin)/admin/exercises/page.tsx` является "God Component". Он управляет состоянием модальных окон, логикой удаления, состоянием загрузки и отображением таблицы.
        *   **Декомпозиция**: Его следует разбить:
            1.  `ExercisesTable.tsx`: Компонент для отображения таблицы.
            2.  `DeleteExerciseDialog.tsx`: Компонент для диалога подтверждения удаления.
            3.  Родительский компонент `page.tsx` должен только получать данные и передавать их дочерним компонентам, а не управлять всем их внутренним состоянием.

2.  **Антипаттерны и "Code Smells"**:
    *   **Prop Drilling**: Не обнаружено серьезных случаев prop drilling, так как проект активно использует композицию компонентов и Server Components, которые получают данные на верхнем уровне.
    *   **Неправильное разграничение Client/Server Components**:
        *   **Найдено**: `/src/app/(admin)/admin/exercises/page.tsx` помечен как `'use client'`, хотя мог бы быть серверным компонентом, который получает данные и передает их в клиентский компонент-обертку. Это увеличивает размер клиентского бандла.
        *   **Рекомендация**: Превратить `page.tsx` в Server Component, который асинхронно получает `initialExercises`, а всю интерактивную логику вынести в дочерний `AdminExercisesClient.tsx`.
    *   **Злоупотребление `useEffect`**: В целом, `useEffect` используется корректно. В `PWAInstallPrompt.tsx` и `PushNotificationManager.tsx` он используется для подписки на события `window`, что является правильным паттерном.

3.  **Анализ системы линтинга и форматирования**:
    *   **Конфигурация**: Проект использует **Biome** (`biome.json`) для форматирования и линтинга, а также **ESLint** (`eslint.config.mjs`). Это может привести к конфликтам. Судя по `package.json` (`lint:staged` использует Biome), **Biome является предпочтительным инструментом**.
    *   **Скрипты**: В `package.json` есть скрипты `lint` (для Next.js/ESLint) и `lint:staged` (для Biome). Это подтверждает наличие двух конкурирующих систем.
    *   **Рекомендация**: Следует выбрать один инструмент для линтинга (предпочтительно Biome, так как он уже используется для форматирования и в pre-commit хуках) и удалить конфигурацию второго, чтобы избежать путаницы и конфликтов правил.

### **3. Анализ стека и зависимостей**

1.  **Next.js (App Router)**:
    *   **Использование**: Возможности App Router (Server/Client Components, Server Actions) используются корректно и к месту. Стратегия "Server Components first" прослеживается.
    *   **Кэширование**: Стратегия кэширования не управляется явно (нет опций `cache` или `revalidate` в `fetch`). Next.js по умолчанию агрессивно кэширует `fetch` в Server Components. Для динамических данных, таких как ранги, это может приводить к отображению устаревшей информации.
    *   **Рекомендация**: Для страниц с динамическими данными (ранги, сабмишены) следует добавить `revalidate` или использовать `cache: 'no-store'`, чтобы данные всегда были актуальными.

2.  **Supabase**:
    *   **Безопасность**:
        *   **Ключи**: `service_role key` не обнаружен на клиенте. Используется правильный подход с `anon_key` на клиенте и `createServerClient` на сервере.
        *   **RLS**: Миграции показывают, что **RLS включен** для всех ключевых таблиц (`user_profiles`, `submissions`, `exercises`). Политики покрывают основные сценарии (пользователь может редактировать только свои данные, публичные данные доступны для чтения). Это **отличная практика**.
    *   **Эффективность запросов**:
        *   Запросы в основном используют `.select('*')`. Это может быть неэффективно, если таблица содержит много колонок, а нужны только некоторые.
        *   **N+1 запросы**: В `/src/libs/rankings.ts` сначала получается список пользователей, а затем в цикле для каждого из них (неявно через `medals.forEach`) подсчитываются медали. Хотя это и не N+1 SQL-запрос, это может быть неэффективно на большом количестве пользователей.
        *   **Рекомендация**: Использовать `.select('column1, column2')` для получения только необходимых данных. Для подсчета медалей лучше создать функцию в БД (RPC), которая будет делать это эффективнее.

3.  **Tailwind CSS**:
    *   **Консистентность**: Использование утилит консистентно. Директива `@apply` не используется, что соответствует философии Tailwind. Компоненты из `shadcn/ui` интегрированы правильно.

### **4. Глубокий Аудит Безопасности**

В ходе анализа не было выявлено критических уязвимостей вроде SQL-инъекций (предотвращается использованием Supabase Client), прямого доступа к `service_role key` на клиенте или серьезных проблем с авторизацией (благодаря RLS). Однако, есть несколько моментов, требующих внимания.

*   **Уязвимость валидации ввода (потенциальная)**:
    *   **Описание**: В компоненте `/src/app/(app)/exercises/[id]/page.tsx` используется `dangerouslySetInnerHTML={{ __html: exercise.description || '' }}`. Если описание упражнения может быть введено пользователем (например, админом) без должной санации, это открывает вектор для **XSS-атак**. Админ может внедрить вредоносный скрипт в описание, который выполнится у всех пользователей, просматривающих это упражнение.
    *   **PoC**: Админ создает упражнение с описанием: `<img src=x onerror="alert('XSS')">`.
    *   **Рекомендация**: Заменить `dangerouslySetInnerHTML` на парсинг Markdown с помощью безопасной библиотеки (например, `marked` с опцией `sanitize: true`) или использовать `DOMPurify` для санации HTML перед рендерингом.

*   **Неограниченное потребление ресурсов (потенциальное)**:
    *   **Описание**: На страницах с пагинацией или "бесконечной прокруткой" (например, список сабмишенов) отсутствует ограничение на максимальное количество запрашиваемых данных. Если API позволяет запрашивать большое количество записей (например, через параметры `limit`/`offset` в URL), злоумышленник может вызвать DoS-атаку, запрашивая миллионы записей.
    *   **Рекомендация**: Всегда устанавливать максимальный `limit` на стороне сервера (в Route Handlers или Server Actions) и не доверять лимиту, приходящему от клиента.

**Таблица уязвимостей**

| Название уязвимости | Описание уязвимости | Путь к файлу | Вектор CVSS | Уверенность | Шаги для эксплуатации |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **Stored XSS** | Использование `dangerouslySetInnerHTML` с данными из БД без санации. | `/src/app/(app)/exercises/[id]/page.tsx` | `CVSS:3.1/AV:N/AC:L/PR:H/UI:R/S:C/C:L/I:L/A:N` (Средняя) | Высокая | 1. Админ входит в систему. 2. Создает/редактирует упражнение. 3. В поле "описание" вводит `<script>alert(document.cookie)</script>`. 4. Любой пользователь, открывший эту страницу, выполнит скрипт. |

### **5. Анализ Производительности**

1.  **Размер клиентского бандла**:
    *   **Кандидаты**: Основные кандидаты на увеличение бандла - это тяжелые библиотеки, если они используются в клиентских компонентах. На данный момент таких библиотек не замечено, но стоит обратить внимание на компонент `ExerciseModalForm`, который может содержать сложную логику и зависимости.
    *   **Рекомендация**: Использовать `next/dynamic` для модальных окон, которые не видны при первой загрузке. Например: `const ExerciseModalForm = dynamic(() => import('@/features/exercises/ExerciseModalForm'))`.
2.  **Оптимизация изображений**:
    *   Компонент `next/image` используется в большинстве мест (`HeroSection`, `ExerciseCard`), что является хорошей практикой.
3.  **Мемоизация и ре-рендеры**:
    *   В проекте активно используется `useCallback` в `rankings-client.tsx` для мемоизации обработчиков. Это правильный подход для предотвращения лишних ре-рендеров дочерних компонентов. В остальных местах использование `useMemo` и `useCallback` минимально, что говорит об отсутствии преждевременной оптимизации.

### **6. Итоговый План Улучшений (Action Plan)**

*   **Приоритет: Высокий (Критично)**
    1.  **Проблема**: Уязвимость Stored XSS через `dangerouslySetInnerHTML`.
        *   **Расположение**: `/src/app/(app)/exercises/[id]/page.tsx`.
        *   **Решение**: Заменить `dangerouslySetInnerHTML` на безопасный рендеринг Markdown/HTML, используя библиотеку `DOMPurify` или аналог для санации данных перед отображением.
    2.  **Проблема**: Конфликт между Biome и ESLint.
        *   **Расположение**: `package.json`, `biome.json`, `eslint.config.mjs`.
        *   **Решение**: Выбрать один инструмент для линтинга (рекомендуется Biome), удалить конфигурацию и зависимости второго. Обновить скрипт `lint` в `package.json`.

*   **Приоритет: Средний (Рекомендуется)**
    1.  **Проблема**: Неэффективное получение данных (использование `select('*')` и потенциальный N+1 при подсчете медалей).
        *   **Расположение**: Различные Server Components, `/src/libs/rankings.ts`.
        *   **Решение**: Заменить `select('*')` на явное перечисление необходимых колонок. Создать RPC-функцию в Supabase для эффективного подсчета медалей одним запросом.
    2.  **Проблема**: "God Component" на странице администрирования упражнений.
        *   **Расположение**: `/src/app/(admin)/admin/exercises/page.tsx`.
        *   **Решение**: Провести рефакторинг: вынести таблицу и диалоговые окна в отдельные компоненты, оставив в `page.tsx` только логику получения данных и композицию UI.
    3.  **Проблема**: Отсутствие стратегии кэширования для динамических данных.
        *   **Расположение**: Страницы, отображающие ранги и сабмишены.
        *   **Решение**: Добавить опцию `{ next: { revalidate: 60 } }` (например, перепроверка каждые 60 секунд) или `cache: 'no-store'` к вызовам `fetch` (внутри клиента Supabase) на динамических страницах.

*   **Приоритет: Низкий (Желательно)**
    1.  **Проблема**: Дублирование логики в `FeaturedExercisesList.tsx` и `ExerciseLibraryList.tsx`.
        *   **Расположение**: Указанные файлы.
        *   **Решение**: Создать кастомный хук `useActiveCardObserver`, который инкапсулирует логику `IntersectionObserver` и возвращает `activeCardId`.
    2.  **Проблема**: Усложненная утилита `cn`.
        *   **Расположение**: `/src/libs/utils.ts`.
        *   **Решение**: Упростить функцию `cn`, оставив только `twMerge(clsx(inputs))`, и исправить классы в местах вызова, если это необходимо.
