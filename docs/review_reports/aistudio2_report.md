## **Аудиторский Отчет: Armwrestling Power Arena**

### **Общая Оценка**

Проект "Armwrestling Power Arena" представляет собой хорошо структурированное веб-приложение, созданное на современном стеке (Next.js 14+ App Router, React 19, Supabase, Tailwind CSS). Архитектура демонстрирует четкое понимание передовых практик, таких как разделение на клиентские и серверные компоненты, использование Server Actions для мутаций данных и применение Row Level Security (RLS) для защиты данных на уровне базы. Документация проекта обширна и детализирована, что является огромным плюсом.

Несмотря на прочный фундамент, существуют области для улучшения, касающиеся консистентности, полноты тестирования, некоторых аспектов производительности и устранения остаточных проблем, отмеченных в предыдущих ревью.

---

### **1. Обзор высокого уровня: Архитектура и Потоки Данных**

#### **1.0. Анализ Документации**

*   **Полнота и Актуальность**: Документация в директории `docs/` находится на высоком уровне. Файлы, такие как `arm_pwr_arena_specs.md`, `authentication.md` и `database.md`, предоставляют исчерпывающее описание предполагаемой архитектуры и функциональности. Это значительно упрощает аудит и погружение в проект. Однако, файл `docs/todo.md` не полностью синхронизирован с реальным состоянием кодовой базы — некоторые отмеченные как выполненные задачи не имеют тестов или реализованы частично.
*   **Соблюдение Правил**: Проект в целом следует правилам, описанным в `docs/rules/`, особенно критически важным, таким как `nextjs_supabase.mdc` (правильное использование `@supabase/ssr`). Однако есть небольшие отклонения (например, наличие `// TODO` комментариев, что противоречит `fds.mdc`), которые будут отмечены ниже.

#### **1.1. Диаграмма Архитектуры**

```mermaid
graph TD
    subgraph "Клиент (Браузер)"
        A[Next.js Client Components] --> B{ShadCN UI};
        A --> C[Кастомные хуки, /hooks];
        C --> D[Клиент Supabase, /libs/supabase/client.ts];
        A --> E[Server Actions, /features/**/actions.ts];
    end

    subgraph "Сервер (Next.js)"
        F[Next.js Server Components, /app] --> G[Серверный клиент Supabase, /libs/supabase/server.ts];
        E --> G;
        H[Route Handlers, /app/api] --> G;
        I[Middleware, /middleware.ts] --> G;
    end

    subgraph "Backend as a Service (Supabase)"
        J[Supabase Auth]
        K[Supabase DB (PostgreSQL)];
        L[Supabase Storage];
    end

    E -- Вызов RPC и CRUD --> K;
    G -- Запросы (SELECT, RPC) --> K;
    H -- Запросы --> K;
    D -- Запросы к Storage --> L;
    I -- Управление сессией --> J;
    C -- Управление сессией --> J;
```

#### **1.2. Описание Структуры Репозитория**

Структура проекта в целом следует принципам **Feature-Sliced Design (FSD)**, что является отличной практикой для масштабируемости и поддержки.

*   `src/app`: Ядро приложения на **Next.js App Router**. Содержит маршруты, страницы (`page.tsx`), лейауты (`layout.tsx`), а также обработчики API (`route.ts`) и серверные действия (`actions.ts`).
*   `src/components`: Содержит переиспользуемые UI-компоненты. `ui/` зарезервирован для "чистых" компонентов ShadCN, в то время как другие поддиректории (`features`, `home`, `profile`) содержат более сложные, составные компоненты.
*   `src/features`: Ключевая директория для FSD. Каждая поддиректория инкапсулирует бизнес-логику конкретной фичи (аутентификация, упражнения, сабмишены), включая компоненты, хуки, серверные действия и схемы валидации.
*   `src/libs`: Библиотеки и утилиты, не привязанные к UI. `supabase/` для клиентов Supabase, `permissions/` для RBAC, `validation/` для Zod-схем.
*   `src/hooks`: Переиспользуемые React-хуки (`useIntersectionObserver`, `usePermissions`).
*   `src/shared`: Код, который может быть использован в любой части приложения и не имеет зависимостей от других слоев FSD (например, общие типы, UI-компоненты).
*   `src/styles`: Глобальные стили и переменные Tailwind CSS.
*   `supabase/`: Все, что связано с базой данных: миграции, функции и файл `seed.sql`.
*   `scripts/`: Вспомогательные скрипты для разработки (сидинг, тесты RLS).

#### **1.3. Основные Потоки Данных**

1.  **Аутентификация пользователя**:
    *   **Механизм**: Используется современный подход с `@supabase/ssr`. Сессия управляется через `httpOnly` cookies.
    *   **Поток**: Пользователь взаимодействует с `AuthDialog` (`/features/auth/components/AuthDialog.tsx`). При сабмите вызывается Server Action (`/app/auth/actions.ts`), который выполняет `signInWithPassword` или `signUp` на сервере. Middleware (`/middleware.ts`) на каждом запросе обновляет сессию, вызывая `supabase.auth.getUser()`, что обеспечивает консистентность сессии между клиентом и сервером.
    *   **Консистентность**: Подход очень консистентен. Все ключевые точки (Middleware, Server Actions, Route Handlers) используют правильные обертки для создания клиента Supabase с поддержкой cookies.

2.  **Получение и отображение данных (Read Flow)**:
    *   **Механизм**: Преимущественно используются **серверные компоненты**.
    *   **Поток**: Страница, например, `/app/exercises/[id]/page.tsx`, является асинхронным серверным компонентом. Она напрямую вызывает `getSupabaseRouteHandlerClient`, выполняет запрос к БД для получения данных об упражнении и рендерит HTML на сервере. Это эффективно и безопасно. Клиентские компоненты, которым нужны данные, получают их через пропсы.

3.  **Изменение данных (Write Flow)**:
    *   **Механизм**: Используются **Server Actions**.
    *   **Поток**: Форма, например, `/features/exercises/ExerciseForm.tsx`, при сабмите вызывает Server Action (`createExercise` или `updateExercise` из `/features/exercises/actions.ts`). Это действие выполняется на сервере, валидирует данные с помощью Zod, выполняет запрос к БД и, в случае успеха, вызывает `revalidatePath('/admin/exercises')` для обновления кэша затронутой страницы. UI обновляется автоматически при следующем посещении страницы или после `router.refresh()`.

#### **1.4. Диаграммы Связей Файлов (File Interaction Diagrams)**

**Фича: Аутентификация (Sign In)**
```mermaid
graph TD
    A["AuthDialogHandler (URL param listener)"] --> B[AuthDialog];
    B --> C["signInAction (/app/auth/actions.ts)"];
    C --> D["createServerClient (@supabase/ssr)"];
    D --> E[Supabase Auth];
    E --> F[Supabase DB];
    C --> G["redirect() (next/navigation)"];
```

**Фича: Создание Упражнения (Admin)**
```mermaid
graph TD
    A["/app/(admin)/admin/exercises/page.tsx"] --> B[ExerciseModalForm];
    B --> C["ExerciseForm (/features/exercises)"];
    C -- onSubmit --> D["createExercise action (/features/exercises/actions.ts)"];
    D -- checkAdminRole --> E["getSupabaseRouteHandlerClient (/libs/supabase/server.ts)"];
    E --> F[Supabase DB (profiles)];
    D -- insert --> F;
    D --> G["revalidatePath ('/admin/exercises')"];
```

**Фича: Отправка Результата (Submission)**
```mermaid
graph TD
    A["/app/(app)/submit/page.tsx"] --> B[SubmissionForm];
    B --> C["submitPerformance action (/features/submissions/actions.ts)"];
    C --> D["submissionSchema (Zod validation)"];
    C --> E["getSupabaseRouteHandlerClient (/libs/supabase/server.ts)"];
    E --> F[Supabase DB (RPC call)];
    C --> G["revalidatePath ('/account')"];
```

---

### **2. Глубокий анализ качества кода и следования принципам**

#### **2.1. Принципы Программирования**

*   **DRY (Don't Repeat Yourself)**:
    *   **Сильные стороны**: Код в целом хорошо следует принципу DRY. Созданы централизованные утилиты для создания клиентов Supabase (`/libs/supabase`), валидации (`/libs/validation`), управления правами (`/libs/permissions`). UI-компоненты из ShadCN обернуты и переиспользуются.
    *   **Область для улучшения**: В серверных действиях (`features/exercises/actions.ts`, `features/profile/actions.ts`, `features/submissions/evaluationActions.ts`) повторяется логика проверки аутентификации и роли пользователя.
        *   **Решение**: Создать высокоуровневую функцию-обертку или middleware для Server Actions, которая бы инкапсулировала проверку прав доступа. Например:
            ```typescript
            // libs/actions/createProtectedAction.ts
            export function createProtectedAction<T>(
              allowedRoles: UserRole[],
              action: (user: User, payload: T) => Promise<ActionResponse>
            ) {
              return async (payload: T): Promise<ActionResponse> => {
                // Auth & Role check logic here...
                // ...
                return action(user, payload);
              };
            }
            ```

*   **KISS (Keep It Simple, Stupid)**:
    *   **Проблема**: Функция `cn` в `src/libs/utils.ts` содержит кастомную логику для разрешения конфликтов классов отступов (`p-*`, `pt-*` и т.д.).
        ```typescript
        // src/libs/utils.ts
        // ...
        const hasPt = initialParts.some((c) => /^pt-/.test(c));
        const hasPb = initialParts.some((c) => /^pb-/.test(c));
        const filteredParts = initialParts.filter((c) => {
          if (/^p-(?![xy])/.test(c)) {
            if (hasPt || hasPb) return false;
          }
          // ...
        });
        return twMerge(filteredParts);
        ```
    *   **Анализ**: Это решение, хотя и умное, усложняет стандартную утилиту и может быть неожиданным для новых разработчиков. `tailwind-merge` уже отлично справляется с большинством конфликтов. Такое усложнение оправдано только при наличии очень специфических требований к стилям, которые невозможно решить иначе.
    *   **Решение**: Упростить функцию `cn`, оставив только стандартную комбинацию `clsx` и `twMerge`. Это сделает ее поведение предсказуемым и соответствующим общепринятым практикам.
        ```typescript
        // Рекомендуемая упрощенная версия
        import { type ClassValue, clsx } from 'clsx';
        import { twMerge } from 'tailwind-merge';

        export function cn(...inputs: ClassValue[]) {
          return twMerge(clsx(inputs));
        }
        ```

*   **SOLID (Single Responsibility Principle)**:
    *   **Проблема**: Компонент `src/widgets/Navbar.tsx` (200+ строк) берет на себя слишком много ответственности: управление видимостью при скролле, состоянием мобильного меню, состоянием диалога аутентификации.
    *   **Решение**: Декомпозировать логику с помощью кастомных хуков.
        1.  **`useNavbarVisibility()`**: Вынести логику с `useEffect` и `lastScrollY` в этот хук, который будет возвращать `isVisible`.
        2.  **`useMobileMenu()`**: Вынести состояние `isOpen` и функцию `toggleMenu` в этот хук.
        3.  **`useAuthDialog()`**: Логика `authDialogOpen` и `handleAuthClick` также может быть вынесена в отдельный хук для чистоты.

#### **2.2. Антипаттерны и "Code Smells"**

*   **Prop Drilling**: В проекте практически отсутствует благодаря грамотному использованию Server Components (передача данных сверху вниз) и `AuthContext` для глобального состояния аутентификации. Это сильная сторона архитектуры.
*   **Неправильное разграничение Client/Server Components**: Анализ не выявил серьезных нарушений. Компоненты, использующие хуки (`useState`, `useEffect`), корректно помечены как `'use client'`.
*   **Злоупотребление `useEffect`**: Как отмечено в `docs/react-19-migration.md`, команда осознанно перешла от `useEffect` для выборки данных к Server Actions и Server Components, что является лучшей практикой. Оставшиеся `useEffect` (в `Navbar.tsx` для скролла, в `PWAInstallPrompt.tsx` для событий PWA) используются по назначению.

#### **2.3. Анализ Системы Линтинга и Форматирования**

*   **Конфликт Конфигураций**: В проекте одновременно присутствуют `eslint.config.mjs` и `biome.json`. Скрипт в `package.json` (`"lint:staged": "biome check..."`) указывает на то, что **Biome** является основным инструментом.
*   **Решение**: Рекомендуется **удалить `eslint.config.mjs` и зависимость `eslint`**, чтобы избежать путаницы и иметь единый источник правил для форматирования и линтинга. Конфигурация Biome в `biome.json` выглядит полной и адекватной.

---

### **3. Анализ Стека и Зависимостей**

1.  **Next.js (App Router)**:
    *   **Использование**: Возможности App Router используются эффективно. Server Components для рендеринга страниц, Server Actions для мутаций, Route Handlers для API-эндпоинтов (`/api/exercises`).
    *   **Кэширование**: Стратегия кэширования реализована через `revalidatePath` в Server Actions, что является правильным подходом для инвалидации данных после их изменения.

2.  **Supabase**:
    *   **Безопасность (RLS)**: **Это сильная сторона проекта.** Миграции (`supabase/migrations/`) содержат подробные политики RLS для таблиц `profiles`, `exercises` и `submissions`. Они четко разграничивают права для `anon`, `authenticated` и ролей (`admin`, `grandmaster`). Например, триггер `prevent_profile_field_update` в миграции `profiles` не позволяет пользователю самому себе сменить роль — это отличная защитная мера.
    *   **Эффективность запросов**: В большинстве случаев запросы выглядят адекватно. Использование `.select('column1, column2')` вместо `.select('*')` применяется. В сложных случаях, как с рейтингами, используется вызов RPC-функции (`get_ranked_profiles`), что является отличной практикой для оптимизации. Потенциальная проблема: `select('*')` на странице `/account` — хотя это для одного пользователя, в будущем это может стать привычкой. Лучше всегда явно указывать нужные поля.

3.  **Tailwind CSS**:
    *   **Консистентность**: Использование Tailwind консистентно. Цвета, отступы и шрифты определены в `tailwind.config.ts` через CSS-переменные, что соответствует лучшим практикам ShadCN. Злоупотребление `@apply` не обнаружено.

---

### **4. Глубокий Аудит Безопасности**

Проект демонстрирует высокий уровень зрелости в вопросах безопасности, благодаря чему критических уязвимостей, таких как SQL-инъекции или прямой доступ к `service_role key` на клиенте, не обнаружено. Основные защитные механизмы (RLS, валидация на сервере) реализованы корректно.

**Найденные уязвимости и потенциальные риски:**

1.  **Недостаточная валидация на стороне сервера в некоторых сценариях (Уязвимость бизнес-логики)**
    *   **Путь к файлу**: `src/features/submissions/actions.ts` (в `submitPerformance`)
    *   **Описание**: Server Action `submitPerformance` проверяет аутентификацию и лимит по времени, но не проверяет, существует ли `exerciseId`, переданный с клиента, в базе данных. Злоумышленник может отправить запрос с ID несуществующего или удаленного упражнения.
    *   **Воздействие**: Низкое. Это не приведет к утечке данных, но может создать "мусорные" записи в таблице `submissions` со ссылкой на несуществующее упражнение, что может вызвать ошибки в других частях системы (например, при отображении истории).
    *   **PoC**:
        1.  Авторизоваться как `athlete`.
        2.  Перехватить запрос от формы сабмита.
        3.  Заменить `exerciseId` на несуществующий ID (например, `99999`).
        4.  Отправить запрос. Сервер создаст запись.
    *   **Рекомендация**: Перед созданием `submission`, добавить проверку существования упражнения в `submitPerformance`:
        ```typescript
        // Внутри submitPerformance, после валидации Zod
        const { data: exerciseExists, error: exerciseError } = await supabase
          .from('exercises')
          .select('id')
          .eq('id', numericExerciseId)
          .single();

        if (exerciseError || !exerciseExists) {
          return { error: 'The selected exercise does not exist.' };
        }
        // ... остальная логика
        ```

2.  **Небезопасный редирект в колбэке OAuth (уже исправлено, но стоит отметить)**
    *   **Путь к файлу**: `src/app/auth/callback/route.ts`
    *   **Описание**: Предыдущие версии кода могли быть уязвимы для Open Redirect, если параметр `redirectTo` не валидировался. Текущая реализация содержит функцию `sanitizeRedirectUrl`, которая корректно проверяет, что редирект происходит в пределах того же `origin`.
    *   **Оценка**: Это **хороший пример** исправленной уязвимости. Уязвимости нет, но важность этой функции стоит подчеркнуть.

3.  **Отсутствие CSRF-защиты на формах без Server Actions (Теоретический риск)**
    *   **Описание**: Все основные формы для мутации данных используют Server Actions, которые имеют встроенную CSRF-защиту. Если в будущем будут добавлены формы, отправляющие POST-запросы на Route Handlers, потребуется ручная реализация CSRF-токенов (например, через `double-submit cookie`).
    *   **Воздействие**: На данный момент низкое, так как уязвимых форм нет.
    *   **Рекомендация**: Придерживаться использования Server Actions для всех форм, изменяющих состояние.

**Таблица Уязвимостей**

| Название уязвимости | Описание уязвимости | Путь к файлу | Вектор CVSS | Уверенность | Шаги для эксплуатации |
| :--- | :--- | :--- | :--- | :--- | :--- |
| Недостаточная валидация (ID упражнения) | Отсутствие проверки существования `exerciseId` на сервере при создании submission. | `src/features/submissions/actions.ts` | `CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:L/A:N` (3.5 Low) | High | 1. Авторизоваться. 2. Отправить запрос на `submitPerformance` с ID несуществующего упражнения. 3. Убедиться, что запись создана в БД. |

---

### **5. Анализ Производительности**

1.  **Размер клиентского бандла**:
    *   **Кандидаты**: Основные "тяжелые" компоненты, загружаемые на клиенте — это формы и диалоги со всеми зависимостями (`@tanstack/react-form`, `zod`, компоненты `shadcn`). Например, `SubmissionForm.tsx` и `EditProfileForm.tsx`.
    *   **Решение**: Текущий размер бандла, вероятно, приемлем. Однако, если в будущем будут добавлены тяжелые библиотеки (например, для графиков или сложной визуализации данных), рекомендуется использовать динамический импорт (`next/dynamic`) для компонентов, которые не нужны на первой загрузке. Для анализа можно использовать `@next/bundle-analyzer`.

2.  **Оптимизация изображений**:
    *   **Анализ**: В проекте используется компонент `next/image` в `Footer.tsx` и `Navbar.tsx`. Это хорошо. Однако, в `MedalSystemSection.tsx`, для медалей также используется `next/image`, что является правильной практикой.
    *   **Рекомендация**: Продолжать использовать `next/image` для всех изображений, которые являются частью контента. Для фоновых изображений (как градиент в `body`) использование CSS является верным.

3.  **Мемоизация и ре-рендеры**:
    *   **Анализ**: В компонентах, где есть сложная логика в `useEffect` (например, `FeaturedExercisesList.tsx`), используется `useCallback` для мемоизации функций. Это говорит о понимании оптимизации ре-рендеров.
    *   **Рекомендация**: На данном этапе нет очевидных проблем с производительностью ре-рендеров. Рекомендуется использовать React DevTools Profiler для выявления узких мест по мере роста сложности клиентских компонентов.

---

### **6. Итоговый План Улучшений (Action Plan)**

#### **Приоритет: Высокий (Критично)**

1.  **Проблема**: Потенциальная уязвимость бизнес-логики — создание `submission` для несуществующего `exercise`.
    *   **Расположение**: `src/features/submissions/actions.ts`
    *   **Решение**: В Server Action `submitPerformance` добавить проверку существования `exerciseId` в базе данных перед созданием новой записи.

#### **Приоритет: Средний (Рекомендуется)**

1.  **Проблема**: Конфликт конфигураций линтера (ESLint и Biome).
    *   **Расположение**: `eslint.config.mjs`, `biome.json`, `package.json`.
    *   **Решение**: Удалить `eslint.config.mjs` и соответствующие зависимости из `package.json`. Оставить Biome как единственный инструмент для линтинга и форматирования, чтобы избежать путаницы.

2.  **Проблема**: Недостаточное тестовое покрытие.
    *   **Расположение**: Весь проект. Множество файлов `.test.tsx` существуют, но `docs/todo.md` показывает, что многие тесты не написаны.
    *   **Решение**: Систематически покрывать тестами бизнес-логику в Server Actions (`actions.test.ts`), сложные компоненты (`SubmissionForm.test.tsx`) и утилиты. Установить и отслеживать порог покрытия (например, 80%) в CI.

3.  **Проблема**: Дублирование логики проверки прав доступа в Server Actions.
    *   **Расположение**: `src/features/*/actions.ts`.
    *   **Решение**: Создать функцию-обертку `createProtectedAction`, которая будет принимать массив ролей и само действие. Эта обертка будет инкапсулировать логику проверки аутентификации и роли, делая код действий чище и безопаснее.

#### **Приоритет: Низкий (Желательно)**

1.  **Проблема**: Избыточное усложнение утилиты `cn`.
    *   **Расположение**: `src/libs/utils.ts`.
    *   **Решение**: Упростить функцию `cn`, оставив только стандартную композицию `twMerge(clsx(inputs))`. Это повысит предсказуемость и упростит поддержку.

2.  **Проблема**: "Божественный компонент" `Navbar`.
    *   **Расположение**: `src/widgets/Navbar.tsx`.
    *   **Решение**: Вынести логику управления видимостью при скролле и состоянием мобильного меню в кастомные хуки (`useNavbarVisibility`, `useMobileMenu`).

3.  **Проблема**: Рассинхронизация `// TODO` комментариев и `docs/todo.md`.
    *   **Расположение**: Множество файлов по всему проекту.
    *   **Решение**: Провести ревизию всех `// TODO` комментариев. Перенести их в `docs/todo.md` или другую систему трекинга задач, после чего удалить из кода. Это создаст единый источник правды для будущих задач.


### **7. Комментарии к Архитектуре и Структуре**

*   **`src/shared/libs` vs `src/libs`**: Наличие двух директорий с названием `libs` в разных частях проекта (`src/libs` и `src/shared/libs`) действительно требует пояснения.

Это разделение является попыткой следовать методологии **Feature-Sliced Design (FSD)**, где код структурируется по слоям с четкими правилами зависимостей.

*   **`src/shared`** — это самый нижний, фундаментальный слой. Код в этой папке не должен зависеть от какой-либо бизнес-логики или других слоев проекта (`features`, `widgets`, `app`). Он содержит абсолютно переиспользуемые модули: UI-кит, хелперы, конфигурации, типы.
*   **`src/libs`** — это не стандартная папка FSD. В данном проекте она используется как **прикладной слой (application layer)**, который содержит более высокоуровневую, специфичную для всего приложения бизнес-логику и инфраструктурный код.

Давайте разберем их назначение и содержимое более детально.

### **Сравнительный Анализ: `src/libs` vs `src/shared/libs`**

| Характеристика | `src/shared/libs` | `src/libs` |
| :--- | :--- | :--- |
| **Назначение (Концепция)** | **Фундаментальные, переиспользуемые утилиты.** Код, который можно было бы вынести в отдельный npm-пакет. Он не знает ничего о бизнес-логике "Armwrestling Power Arena". | **Прикладная бизнес-логика и инфраструктура.** Код, который является ядром данного конкретного приложения, управляет его правилами и взаимодействием с внешними сервисами. |
| **Зависимости** | **Не может** импортировать из `features`, `widgets`, `app` или `src/libs`. Может импортировать только другие модули из `src/shared`. | **Может** импортировать из `src/shared/libs`. **Не должен** импортировать из `features`, `widgets` или `app`. |
| **Примеры файлов и их роль** | `auth/context.tsx`: Создание `React.Context` для аутентификации. Сам по себе контекст — это универсальный механизм, не привязанный к логике.<br><br>`auth/validation.ts`: Zod-схемы для валидации email/пароля. Это универсальные правила, которые могут быть использованы в любом проекте. | `permissions/`: **Вся система прав доступа (RBAC)**. Это — ключевая бизнес-логика приложения.<br><br>`supabase/`: **Конфигурация и создание клиентов Supabase.** Это инфраструктурный код, связывающий приложение с конкретным BaaS.<br><br>`rankings.ts`: **Логика получения рейтингов.** Специфичный для приложения запрос к БД. |

### **Детальный разбор содержимого**

#### `src/shared/libs` - Фундамент

Эта директория содержит низкоуровневые, не зависящие от бизнес-контекста "кирпичики".

*   `auth/context.tsx`: Определяет `AuthContext` и `AuthProvider`. Это обертка, которая предоставляет состояние аутентификации дочерним компонентам. Сама по себе она не выполняет логику входа или регистрации, а лишь хранит и передает состояние.
*   `auth/validation.ts`: Содержит Zod-схемы для валидации данных, таких как email и пароль. Эти схемы являются чистыми правилами и могут быть повторно использованы как на клиенте (в формах), так и на сервере (в Server Actions).

#### `src/libs` - Ядро Приложения

Эта директория содержит код, который "заставляет приложение работать" и определяет его уникальную логику.

*   `audit/auditLogger.ts`: Логика аудита действий пользователей. Это чисто прикладная задача, специфичная для требований безопасности этого проекта.
*   `auth/`: Содержит высокоуровневые утилиты для защиты роутов и API (`protectedApi.ts`, `serverPermissions.ts`), которые уже используют `AuthContext` из `shared` и систему прав из `libs/permissions`.
*   `permissions/`: **Ключевой модуль**. Здесь определена вся модель Role-Based Access Control (RBAC): какие роли существуют, какие у них права (`rolePermissions.ts`), как проверять доступ к страницам (`acl.ts`, `pageRoles.ts`). Это сердце бизнес-логики авторизации.
*   `rankings.ts`: Функция `fetchRankings` инкапсулирует сложный запрос к Supabase (включая RPC-вызов) для получения данных для страницы рейтингов. Это специфичная для приложения логика доступа к данным.
*   `supabase/`: Содержит все способы создания клиентов Supabase для разных контекстов (клиент, сервер, middleware). Это инфраструктурный слой, который абстрагирует прямое создание клиентов в компонентах или экшенах.
*   `utils.ts`: Содержит утилиту `cn` для объединения классов Tailwind.
*   `validation/`: Содержит валидацию, связанную с аутентификацией.

### **Проблемы и Рекомендации по Рефакторингу**

Несмотря на логичное разделение, текущая структура имеет несколько недостатков, которые могут привести к путанице:

1.  **Конфликт Имен**: Наличие двух папок с названием `libs` — это **главная проблема**. Это сбивает с толку.
    *   **Решение**: Переименовать `src/libs` в нечто более семантически верное, например, **`src/core`** или **`src/services`**. Это сразу же устранит двусмысленность.

2.  **Дублирование Кода**: Файл `src/libs/validation/auth.ts` и `src/shared/libs/auth/validation.ts` являются дубликатами.
    *   **Решение**: **Удалить `src/libs/validation/`**. Вся универсальная валидация (как схемы для email/пароля) должна находиться исключительно в `src/shared/libs/auth/validation.ts`. Все части приложения, которым нужна эта валидация, должны импортировать ее оттуда.

3.  **Неправильное Расположение Файла**: Утилита `cn` в `src/libs/utils.ts` является классическим примером "общей" утилиты.
    *   **Решение**: Переместить `src/libs/utils.ts` в `src/shared/lib/utils.ts` (создав папку `lib` внутри `shared`, если ее нет). Это соответствует принципам FSD, где все универсальные хелперы лежат в `shared`.

### **Итог**

*   **`src/shared/libs`**: Предназначена для низкоуровневых, полностью переиспользуемых библиотечных модулей (контекст, универсальные схемы валидации).
*   **`src/libs`**: Используется как **прикладной слой** для хранения специфичной для проекта бизнес-логики (система прав доступа) и инфраструктурного кода (клиенты Supabase, логика получения данных).

Рекомендуется провести небольшой рефакторинг для устранения конфликта имен и дублирования, что сделает архитектуру еще более чистой и понятной.
