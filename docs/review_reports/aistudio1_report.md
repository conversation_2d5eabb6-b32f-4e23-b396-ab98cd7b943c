# Armwrestling Power Arena - Codebase Review Report

**TL;DR**: Проект "Armwrestling Power Arena" имеет прочный архитектурный фундамент, основанный на Next.js App Router и Supabase, с хорошей организацией кода, приближенной к Feature-Sliced Design. Документация обширна и задает правильное направление, однако местами отстает от реализации или противоречит ей. Ключевые потоки данных, такие как аутентификация, в основном реализованы с использованием современных и безопасных паттернов (Server Actions, `@supabase/ssr`).

**Основные риски и необходимые действия**:
1.  **Безопасность**: Главный приоритет — это полное и строгое внедрение политик Row Level Security (RLS) для **всех** таблиц и пересмотр прав доступа для функций PostgreSQL (`SECURITY DEFINER`). Также необходимо разрешить конфликт между Biome и ESLint и ужесточить правила линтинга.
2.  **Качество кода**: Обнаружено значительное дублирование кода, особенно в формах и компонентах аутентификации. Требуется рефакторинг для централизации логики и переиспользования компонентов.
3.  **Производительность**: Существуют потенциальные проблемы N+1 запросов при получении данных для рейтингов, что может замедлить работу приложения при масштабировании.
4.  **Тестирование**: Несмотря на наличие конфигурации для тестов, их фактическое количество минимально. Необходимо срочно увеличить тестовое покрытие, особенно для критических бизнес-сценариев.

В целом, проект находится в хорошем состоянии для дальнейшего развития, но требует немедленного внимания к вопросам безопасности и рефакторинга для обеспечения долгосрочной стабильности и масштабируемости.

---

### **1. Обзор высокого уровня: Архитектура и Потоки Данных**

#### **0. Документация проекта**

*   **Полнота и актуальность**: Документация в `docs/` очень подробна и охватывает спецификации, архитектурные решения (миграция на React 19), гайды по стилям, PWA и работе с Supabase. Это сильная сторона проекта. Однако есть расхождения:
    *   `docs/todo.md` показывает много выполненных задач, но тесты для них отсутствуют в кодовой базе (`src/**/*.test.tsx`), что является нарушением правила `react-ts.mdc`.
    *   Документы `review.md` и `review_reports/` указывают на проблемы, которые частично исправлены (например, удаление `dangerouslySetInnerHTML`), но некоторые (конфликт линтеров, отсутствие тестов) остаются актуальными.
    *   Документация по аутентификации (`authentication.md`) описывает идеальную, унифицированную систему, но в коде все еще присутствуют остатки старых подходов или дублирующиеся компоненты (`EmailAuthForm`, `ModernAuthForm`), которые следовало бы удалить.

*   **Соблюдение правил**: Анализ кода показал, что не все правила из `docs/rules/` соблюдаются.
    *   `react-ts.mdc`: Правило "tests must accompany code" массово нарушается.
    *   `fds.mdc`: Есть около 52 комментариев `// TODO`, что нарушает правило о недопустимости TODO в коде.
    *   `sql_style.mdc`: `src/libs/rankings.ts` содержал сырой SQL (исправлено, судя по `review.md`).
    *   `nextjs_supabase.mdc`: Критическое правило по использованию `getAll`/`setAll` для cookies **соблюдается** в `middleware.ts` и `src/app/auth/callback/route.ts`, что является большим плюсом.

#### **1. Диаграмма Архитектуры**

```mermaid
graph TD
    subgraph "Клиент (Браузер)"
        A[Next.js Frontend: React 19, App Router]
        B[Клиентские Компоненты: UI, формы]
        C[PWA Service Worker]
    end

    subgraph "Сервер (Next.js)"
        D[Серверные Компоненты]
        E[Server Actions]
        F[Route Handlers (API)]
        G[Middleware]
    end

    subgraph "Backend as a Service (Supabase)"
        H[Supabase Auth]
        I[Supabase DB: PostgreSQL]
        J[Supabase Storage]
        K[DB Functions/RPC]
    end

    %% Взаимодействия
    A -- "Рендеринг" --> B
    B -- "Вызывает" --> E
    B -- "Запрашивает" --> F
    C -- "Кэширует" --> A

    D -- "Прямой безопасный вызов" --> I
    E -- "Безопасный вызов (CRUD)" --> I
    E -- "Вызывает" --> K
    F -- "Безопасный вызов (R)" --> I

    G -- "Перехватывает запросы к" --> D
    G -- "Проверяет сессию через" --> H

    B -- "Аутентификация" --> H
    E -- "Аутентификация" --> H

    B -- "Загрузка файлов" --> J

    style A fill:#BDEBFF,stroke:#333
    style I fill:#D5E8D4,stroke:#333
    style J fill:#D5E8D4,stroke:#333
    style H fill:#D5E8D4,stroke:#333
    style K fill:#D5E8D4,stroke:#333
    style E fill:#FFE6CC,stroke:#333
    style F fill:#FFE6CC,stroke:#333
```

#### **2. Описание структуры репозитория**

Структура проекта близка к методологии **Feature-Sliced Design (FSD)**, что указано в правилах (`docs/rules/fds.mdc`).

*   `src/app`: Ядро приложения с использованием App Router. Содержит страницы, лейауты, обработчики ошибок и загрузки. Маршруты сгруппированы по логическому назначению (`(admin)`, `(app)`, `auth`).
*   `src/components`: Содержит переиспользуемые UI-компоненты. `ui/` — это компоненты `shadcn/ui`, а остальные — более сложные, составные компоненты (`PageLayout`, `Footer`).
*   `src/features`: Реализация бизнес-логики и пользовательских сценариев. Каждый "слайс" (например, `auth`, `exercises`) содержит свои компоненты, хуки и действия (actions). Это соответствует FSD.
*   `src/libs`: Библиотеки и утилиты, не связанные с конкретной бизнес-логикой. `supabase/` для клиентов Supabase, `permissions/` для RBAC, `validation/` для схем Zod.
*   `src/hooks`: Переиспользуемые React-хуки (`useIntersectionObserver`, `useRoleAccess`).
*   `src/shared`: Код, переиспользуемый между разными слоями FSD (типы, UI-кит, контексты). `shared/libs/auth` содержит унифицированный контекст и валидацию, что является хорошей практикой.
*   `src/styles`: Глобальные стили и переменные CSS.
*   `supabase/`: Конфигурация и миграции для Supabase. `migrations/` содержит SQL-файлы для схемы БД, `functions/` — для RPC. Это стандартная и правильная структура для работы с Supabase CLI.
*   `scripts/`: Вспомогательные Node.js-скрипты для сидинга, тестирования RLS и проверки PWA.

**Принципы**: Структура основана на модульности, разделении ответственности и изоляции фич, что упрощает поддержку и масштабирование.

#### **3. Основные потоки данных (Data Flow)**

*   **Аутентификация пользователя**:
    *   **Механизм**: Процесс унифицирован через `AuthDialog` и `AuthDialogHandler`, которые вызываются либо по прямому клику, либо через URL-параметр (`/?auth=signin`).
    *   **Управление сессией**: Используется пакет `@supabase/ssr`, что является **лучшей практикой**. Сессия управляется через `httpOnly` cookies. `middleware.ts` корректно перехватывает запросы и обновляет сессию пользователя, вызывая `supabase.auth.getUser()`. Это обеспечивает консистентное состояние сессии как на клиенте, так и на сервере.
    *   **Действия**: Регистрация (`signUpAction`) и вход (`signInAction`) реализованы как Server Actions в `src/app/auth/actions.ts`. Они получают `FormData`, валидируют данные с помощью Zod, вызывают Supabase и обрабатывают ответы, включая редиректы. Этот подход безопасен, так как логика выполняется на сервере.

*   **Получение и отображение данных**:
    *   **Серверные компоненты (Server Components)**: Большинство страниц (`/exercises/[id]`, `/rankings`, `/profile/[username]`) являются асинхронными серверными компонентами. Они напрямую вызывают `getSupabaseRouteHandlerClient` или `createClient` для получения данных. Это эффективно и безопасно.
    *   **Клиентские компоненты (Client Components)**: `src/features/submissions/components/ExerciseSelector.tsx` является примером клиентского компонента, который использует `fetch` для вызова API Route Handler (`/api/exercises`) для получения списка упражнений. Это правильный подход для динамических данных, зависящих от действий пользователя на клиенте.
    *   **API Route Handlers**: `src/app/api/exercises/route.ts` используется для предоставления данных клиентским компонентам, которые не могут выполнять прямые запросы к БД. Он корректно проверяет аутентификацию пользователя.

*   **Изменение данных**:
    *   **Server Actions**: Основной механизм для мутаций. Например, создание/обновление упражнений (`src/features/exercises/actions.ts`) и профиля (`src/features/profile/actions.ts`).
    *   **Обновление UI**: После успешного выполнения Server Action используется `revalidatePath`, что является стандартным и правильным способом инвалидации кэша Next.js и обновления данных на страницах. На клиенте для обратной связи используется `sonner` (toasts).

#### **4. Детализированная диаграмма связей файлов (File Interaction Diagram)**

**Фича: Администрирование упражнений (Создание/Редактирование)**
```mermaid
graph TD
    A["/admin/exercises/page.tsx (Client)"] --> B["ExerciseModalForm"];
    B -- "Отображает" --> C["ExerciseForm"];
    C -- "При сабмите вызывает" --> D["/features/exercises/actions.ts#createExercise/updateExercise"];
    D -- "Проверяет роль через" --> E["/libs/permissions/rbac.ts#assertRole"];
    E -- "Вызывает" --> F["/libs/supabase/server.ts#getSupabaseRouteHandlerClient"];
    D -- "Валидирует данные через" --> G["Zod schema"];
    D -- "Вызывает клиент Supabase для" --> H["INSERT/UPDATE в таблицу exercises"];
    D -- "При успехе инвалидирует кэш" --> I["revalidatePath('/admin/exercises')"];
```

**Фича: Отправка результата (Submission)**
```mermaid
graph TD
    subgraph "Страница /submit"
        A["/app/(app)/submit/page.tsx"] --> B["SubmissionForm (Client)"];
    end

    subgraph "Компоненты формы"
        B -- "Использует" --> C["ExerciseSelector"];
        C -- "Делает fetch-запрос к" --> D["/app/api/exercises/route.ts"];
    end

    subgraph "Действие на сервере"
        B -- "При сабмите вызывает Server Action" --> E["/features/submissions/actions.ts#submitPerformance"];
        E -- "Валидирует данные через" --> F["submissionSchema.ts"];
        E -- "Проверяет аутентификацию и лимиты" --> G["Supabase DB (profiles)"];
        E -- "Выполняет RPC-функцию" --> H["create_submission_and_update_profile"];
        H -- "Создает запись в" --> I["Таблица submissions"];
        H -- "Обновляет запись в" --> G;
        E -- "При успехе инвалидирует кэш" --> J["revalidatePath('/account')"];
    end
```

**Фича: Отображение Рейтингов**
```mermaid
graph TD
    A["/app/rankings/page.tsx (Server)"] --> B["rankings-client.tsx (Client)"];
    A -- "Вызывает" --> C["/libs/rankings.ts#fetchRankings"];
    B -- "Управляет фильтрами" --> B;
    C -- "Вызывает RPC-функцию" --> D["get_ranked_profiles"];
    C -- "Вызывает" --> E["Supabase DB (medals)"];
    A -- "Передает данные в" --> F["RankingsTable.tsx"];
```

---

### **2. Глубокий анализ качества кода и следования принципам**

#### **1. Принципы программирования**

*   **DRY (Don't Repeat Yourself)**:
    *   **Нарушение**: Существует несколько форм для аутентификации: `AuthDialog.tsx`, `ModernAuthForm.tsx`, `EmailAuthForm.tsx`. `auth_recent_cahnges_review.md` правильно указывает на их избыточность. `ModernAuthForm` и `AuthDialog` используют `useActionState`, а `EmailAuthForm` — старый подход с `useState`. Это создает несогласованность.
    *   **Предложение**: Удалить `EmailAuthForm.tsx` и `ModernAuthForm.tsx`. Оставить только `AuthDialog.tsx` как единый компонент для аутентификации, который уже использует современный подход с Server Actions.
    *   **Нарушение**: Логика создания клиента Supabase дублируется в разных местах, хотя есть попытки централизации в `libs/supabase`. Например, `src/app/profile/create/page.tsx` создает клиент напрямую, а не через утилиту.
    *   **Предложение**: Строго следовать использованию утилит из `src/libs/supabase`, как рекомендовано в `supabase_client_creation_review.md`.

*   **KISS (Keep It Simple, Stupid)**:
    *   **Избыточная сложность**: `src/middleware.ts` содержит сложную логику для определения публичных маршрутов (`PUBLIC_PREFIXES` и `startsWithAny`). Это можно упростить, используя встроенные возможности Next.js `matcher` в `config` или более декларативный подход с `isPathAllowed` из `libs/permissions/acl.ts`, который уже существует и используется.
    *   **Предложение**: Рефакторить `middleware.ts` для использования `isPathAllowed` для проверки доступа к роутам, что сделает код чище и централизует логику доступа в одном месте.

*   **SOLID**:
    *   **Single Responsibility Principle (SRP)**:
        *   **Нарушение**: `Navbar.tsx` берет на себя слишком много ответственности: управление состоянием видимости при скролле, открытие мобильного меню, открытие диалога аутентификации, отображение ссылок на основе роли.
        *   **Предложение**: Разделить `Navbar` на более мелкие компоненты. Хук `useScrollVisibility` можно вынести для управления видимостью. Логику отображения ссылок можно инкапсулировать в отдельный компонент `NavLinks`. Управление диалогом аутентификации уже вынесено в `AuthDialogHandler`, что хорошо.

#### **2. Антипаттерны и "Code Smells"**

*   **Prop Drilling**: В проекте активно используется `Context` (`AuthProvider`), что минимизирует prop drilling для данных аутентификации. На текущем уровне вложенности компонентов серьезных проблем с пробросом пропсов не обнаружено, архитектура FSD помогает этому.

*   **Неправильное разграничение Client/Server Components**:
    *   **Найдено**: В целом, разделение корректно. Серверные компоненты используются для получения данных, клиентские — для интерактива. Однако `src/app/(admin)/admin/exercises/page.tsx` является клиентским компонентом, хотя мог бы быть серверным, который передает данные клиентскому компоненту-обертке. Это не критично, но серверный компонент-контейнер был бы более идиоматичным.
    *   **Предложение**: Переделать `AdminExercisesPageClient` в обертку, а основную логику загрузки данных вынести в `page.tsx` как серверный компонент, который передаст `initialExercises` и `isAdmin` как пропсы.

*   **Злоупотребление `useEffect`**:
    *   В проекте видна осознанная миграция от `useEffect` к Server Actions, как описано в `docs/react-19-migration.md`.
    *   Корректное использование `useEffect` наблюдается в `PWAInstallPrompt.tsx` (для подписки на события браузера), `FeaturedExercisesList.tsx` (для `IntersectionObserver`) и в `AuthProvider` (для подписки на `onAuthStateChange`). Это хорошие примеры правильного применения хука.

#### **3. Анализ системы линтинга и форматирования**

*   **Конфликт инструментов**: В проекте одновременно присутствуют `biome.json` и `eslint.config.mjs`. Это плохая практика, так как может привести к конфликтам правил и путанице при разработке.
*   **Скрипты**: `package.json` содержит скрипт `"lint": "next lint"`, который запускает ESLint, и `"lint:staged": "biome check..."`, который использует Biome. Это подтверждает конфликт.
*   **Конфигурация**: `eslint.config.mjs` имеет базовую конфигурацию `next/core-web-vitals`. `biome.json` настроен более детально, включая форматирование и организацию импортов.
*   **Предложение**: Необходимо выбрать **один** инструмент как единый источник правды. Учитывая, что Biome предлагает и линтинг, и форматирование с высокой производительностью, рекомендуется полностью перейти на Biome и удалить конфигурацию ESLint.

---

### **3. Анализ стека и зависимостей**

1.  **Next.js (App Router)**:
    *   **Использование**: App Router используется правильно. Серверные компоненты для выборки данных, клиентские для интерактивности, Server Actions для мутаций.
    *   **Кэширование**: В коде явно используется `revalidatePath`, что свидетельствует о понимании стратегии кэширования. Однако нет явного использования `revalidateTag` или настроек `fetch`, что может быть точкой для оптимизации в будущем.

2.  **Supabase**:
    *   **Безопасность (RLS)**: Это **самая сильная и одновременно слабая сторона проекта**. Миграции для `profiles`, `exercises`, `submissions` и `storage` **включают политики RLS**. Это отлично.
        *   **Проблема**: Политики для `exercises` (`20250429202300_create_exercises.sql`) ссылаются на `public.is_admin(auth.uid())`. Эта функция определена в другой миграции (`202504011333_create_user_profiles.sql`) как `SECURITY DEFINER`. Функции `SECURITY DEFINER` **очень опасны**, так как выполняются с правами владельца функции (обычно суперпользователя). Если в такой функции есть уязвимость (например, SQL-инъекция), это может привести к полной компрометации БД.
        *   **Предложение**: Всегда предпочитать `SECURITY INVOKER` и проектировать RLS так, чтобы не требовались повышенные привилегии. Если `SECURITY DEFINER` неизбежен, его код должен быть максимально простым, безопасным и тщательно проверенным. В данном случае `is_admin` выглядит относительно безопасным, но это вектор риска.
    *   **Ключи**: `libs/supabase/credentials.ts` правильно абстрагирует получение ключей, предотвращая их прямое использование в коде. В коде не найдены `service_role key` на клиенте.
    *   **Эффективность запросов**:
        *   **N+1 Проблема**: В `src/libs/rankings.ts` в функции `fetchRankings` сначала делается один запрос для получения пользователей, а затем второй — для получения медалей для всех этих пользователей (`.in('user_id', userIds)`). Это хороший паттерн. Однако, если бы медали получались в цикле для каждого пользователя, это была бы N+1 проблема. Текущая реализация лучше, но может быть еще оптимизирована на уровне БД с помощью `JOIN` и агрегации в одном запросе или RPC.

3.  **Tailwind CSS**:
    *   Использование консистентно. Цвета и другие параметры темы определены через CSS-переменные в `src/styles/globals.css` и подключены в `tailwind.config.ts`, что является лучшей практикой. Злоупотребления `@apply` не замечено.

---

### **4. Глубокий Аудит Безопасности**

**Найденные уязвимости и риски**:

1.  **Недостаточная Авторизация в RPC Функции (Повышение привилегий)**
    *   **Путь**: `supabase/functions/increment_user_points.sql`
    *   **Описание**: Функция `increment_user_points` определена с `SECURITY DEFINER`. Это означает, что она выполняется с правами владельца (суперпользователя), а не вызывающего пользователя. Внутри функции нет проверки, имеет ли вызывающий право изменять очки. Любой аутентифицированный пользователь, который может вызвать эту RPC-функцию, может увеличить очки любому другому пользователю.
    *   **Воздействие**: Любой игрок может накрутить себе или другим неограниченное количество очков, что полностью разрушает систему рейтинга.
    *   **PoC (Proof of Concept)**:
        1. Аутентифицироваться как обычный `athlete`.
        2. Используя клиент Supabase в консоли браузера, выполнить:
           ```javascript
           await supabase.rpc('increment_user_points', { user_id_param: 'свой_uuid', points_param: 1000000 })
           ```
        3. Очки будут успешно начислены.
    *   **Рекомендации**:
        1.  Изменить функцию на `SECURITY INVOKER` и добавить RLS-политику на таблицу `profiles`, разрешающую изменять `total_points` только роли `admin` или `grandmaster`.
        2.  Либо внутри функции `SECURITY DEFINER` добавить проверку роли вызывающего: `IF NOT public.is_admin(auth.uid()) AND NOT public.is_grandmaster(auth.uid()) THEN RAISE EXCEPTION 'Permission denied'; END IF;`.

2.  **Недостаточная валидация на стороне сервера в Server Actions (Обход бизнес-логики)**
    *   **Путь**: `src/features/submissions/evaluationActions.ts` в `evaluateSubmission`.
    *   **Описание**: В действии `evaluateSubmission` есть проверка `if (validatedData.decision === 'approve') { ... }` для начисления очков. Однако `pointsAwarded` и `medalAwarded` приходят с клиента. Злоумышленник может отправить запрос с `decision: 'approve'`, но с `pointsAwarded: 0` и `medalAwarded: 'none'`, чтобы обойти логику начисления очков, но при этом утвердить заявку.
    *   **Воздействие**: Возможность утверждения заявок без начисления медалей и очков, что нарушает целостность данных.
    *   **PoC**: Отправить POST-запрос на эндпоинт Server Action с `decision: 'approve'`, но `pointsAwarded: 0` и `medalAwarded: 'none'`.
    *   **Рекомендации**: Серверная сторона **обязана** сама рассчитывать очки на основе медали (`MEDAL_POINTS[validatedData.medalAwarded]`), а не доверять значению `pointsAwarded` от клиента. Клиент должен отправлять только медаль, а сервер — определять очки.

3.  **Небезопасная конфигурация (Конфликт линтеров)**
    *   **Путь**: `biome.json`, `eslint.config.mjs`, `package.json`.
    *   **Описание**: Наличие двух конкурирующих инструментов для линтинга и форматирования (`Biome` и `ESLint`) создает путаницу и может привести к тому, что проверки безопасности одного из них будут проигнорированы.
    *   **Воздействие**: Снижение качества и безопасности кода, пропуск потенциальных уязвимостей, которые мог бы обнаружить один из инструментов.
    *   **Рекомендации**: Выбрать один инструмент (рекомендуется Biome) и полностью удалить конфигурацию и зависимости другого.

4.  **Устаревшие или уязвимые зависимости** (Потенциальная)
    *   **Путь**: `package.json`.
    *   **Описание**: Хотя явных уязвимых зависимостей при беглом осмотре не видно, аудит не проводился с помощью специализированных инструментов.
    *   **Рекомендации**: Регулярно запускать `npm audit --audit-level=high` или использовать Snyk/Dependabot для автоматического сканирования зависимостей.

| Название уязвимости | Описание уязвимости | Путь к файлу | Вектор CVSS | Уверенность | Шаги для эксплуатации |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **Повышение привилегий через RPC** | Функция `increment_user_points` с `SECURITY DEFINER` не проверяет права вызывающего, позволяя любому пользователю изменять очки. | `supabase/functions/increment_user_points.sql` | `CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:H/A:N` (7.7 High) | High | Аутентифицированный пользователь вызывает RPC через клиент Supabase с ID любого другого пользователя и любым количеством очков. |
| **Обход бизнес-логики** | `evaluateSubmission` доверяет значению `pointsAwarded` от клиента, позволяя утвердить заявку без начисления очков. | `src/features/submissions/evaluationActions.ts` | `CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:L/A:N` (4.3 Medium) | High | Отправить запрос на Server Action с `decision: 'approve'`, но с `pointsAwarded: 0`. |
| **Небезопасная конфигурация** | Конфликт между Biome и ESLint может привести к пропуску проверок безопасности одним из инструментов. | `package.json`, `biome.json`, `eslint.config.mjs` | `CVSS:3.1/AV:L/AC:L/PR:H/UI:N/S:U/C:N/I:L/A:N` (2.8 Low) | High | Разработчик использует только один инструмент для проверки, который не настроен на поиск определенных уязвимостей, в то время как второй настроен. |

---

### **5. Анализ Производительности**

1.  **Размер клиентского бандла**:
    *   **Анализ**: В `package.json` нет очевидно "тяжелых" библиотек (типа `moment.js`), которые бы неоправданно увеличивали бандл. Использование `lucide-react` для иконок является хорошей практикой (tree-shaking).
    *   **Предложение**: Рекомендуется добавить в проект `@next/bundle-analyzer` и периодически анализировать состав клиентского бандла, чтобы выявлять компоненты или библиотеки, которые можно загружать динамически (`next/dynamic`).

2.  **Оптимизация изображений**:
    *   **Анализ**: В коде (`Navbar.tsx`, `ExercisesSection.tsx`) используется компонент `next/image`. Это **отличная практика**, которая обеспечивает автоматическую оптимизацию изображений.
    *   **Предложение**: Продолжать использовать `next/image` для всех изображений, требующих оптимизации.

3.  **Мемоизация и ре-рендеры**:
    *   **Анализ**: В клиентских компонентах, где есть обработчики событий или сложные вычисления, используется `useCallback` и `useMemo` (например, `MedalSelector.tsx`, `FeaturedExercisesList.tsx`). Это говорит о понимании проблем лишних ре-рендеров.
    *   **Предложение**: Продолжать следовать этому паттерну. Рекомендуется использовать `React DevTools Profiler` для выявления компонентов, которые ре-рендерятся слишком часто, и применять мемоизацию точечно, где это необходимо.

---

### **6. Итоговый План Улучшений (Action Plan)**

#### **Приоритет: Высокий (Критично)**

1.  **Проблема**: Уязвимость повышения привилегий в RPC-функции `increment_user_points`.
    *   **Расположение**: `supabase/functions/increment_user_points.sql`.
    *   **Решение**: Переписать функцию, добавив проверку роли вызывающего (`is_admin` или `is_grandmaster`), либо изменить на `SECURITY INVOKER` и настроить соответствующие RLS-политики на таблице `profiles`.
2.  **Проблема**: Неполная серверная валидация в Server Action `evaluateSubmission`.
    *   **Расположение**: `src/features/submissions/evaluationActions.ts`.
    *   **Решение**: Убрать доверие к `pointsAwarded` от клиента. Рассчитывать очки на сервере на основе `medalAwarded` и констант `MEDAL_POINTS`.
3.  **Проблема**: Конфликт инструментов линтинга и форматирования.
    *   **Расположение**: `package.json`, `biome.json`, `eslint.config.mjs`.
    *   **Решение**: Принять решение об использовании одного инструмента (рекомендуется `Biome`). Удалить файлы конфигурации, зависимости и скрипты для второго инструмента. Настроить CI для запуска выбранного линтера на всех коммитах.
4.  **Проблема**: Отсутствие тестов для критически важной бизнес-логики.
    *   **Расположение**: По всему проекту.
    *   **Решение**: Начать написание интеграционных тестов (Vitest) для всех Server Actions (`auth`, `submissions`, `profile`, `exercises`). Покрыть тестами RLS-политики и RPC-функции.

#### **Приоритет: Средний (Рекомендуется)**

1.  **Проблема**: Дублирование кода в компонентах аутентификации.
    *   **Расположение**: `src/features/auth/components/`.
    *   **Решение**: Провести рефакторинг. Удалить `EmailAuthForm.tsx` и `ModernAuthForm.tsx`. Сделать `AuthDialog.tsx` единственным компонентом, отвечающим за модальное окно аутентификации.
2.  **Проблема**: "Божественный компонент" `Navbar`.
    *   **Расположение**: `src/widgets/Navbar.tsx`.
    *   **Решение**: Декомпозировать компонент. Вынести логику видимости при скролле в кастомный хук `useNavbarVisibility`. Создать отдельный компонент `NavLinks` для отображения навигационных ссылок на основе роли.
3.  **Проблема**: Потенциальная N+1 проблема или неэффективный запрос в `fetchRankings`.
    *   **Расположение**: `src/libs/rankings.ts`.
    *   **Решение**: Переписать `fetchRankings` для использования одного SQL-запроса с `JOIN` и агрегацией, либо создать view или RPC-функцию в Supabase, которая будет возвращать все необходимые данные за один вызов.
4.  **Проблема**: Неконсистентное создание клиента Supabase.
    *   **Расположение**: `src/app/profile/create/page.tsx` и другие возможные места.
    *   **Решение**: Провести аудит всех вызовов `createClient` и `createBrowserClient`. Убедиться, что везде используются централизованные утилиты из `src/libs/supabase`.

#### **Приоритет: Низкий (Желательно)**

1.  **Проблема**: Множественные комментарии `// TODO` в кодовой базе.
    *   **Расположение**: По всему проекту.
    *   **Решение**: Перенести все `// TODO` в таск-трекер (например, GitHub Issues), приоритизировать и удалить из кода.
2.  **Проблема**: Неполная документация по некоторым компонентам.
    *   **Расположение**: `src/components`, `src/features`.
    *   **Решение**: Добавить JSDoc/TSDoc комментарии к ключевым компонентам и функциям, описывая их назначение, пропсы и возвращаемые значения.
3.  **Проблема**: Сложная логика определения публичных маршрутов в middleware.
    *   **Расположение**: `src/middleware.ts`.
    *   **Решение**: Рефакторить middleware для использования централизованной функции `isPathAllowed` из `libs/permissions/acl.ts`.
