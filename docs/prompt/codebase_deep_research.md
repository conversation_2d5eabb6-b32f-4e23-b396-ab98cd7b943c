Project Path: `{{ absolute_code_path }}`

Основная цель данного задания — провести исчерпывающий аудит кодовой базы проекта "Armwrestling Power Arena". Результатом должен стать структурированный отчет, который глубоко анализирует архитектуру, качество кода, производительность, проводит углубленный аудит безопасности и предоставляет четкий, приоритизированный план действий для рефакторинга и улучшения проекта.

Source Tree:

```
{{ source_tree }}
```

{{#each files}}
{{#if code}}
`{{path}}`:

```
{{code}}
```
{{/if}}
{{/each}}

Проанализируй предоставленную кодовую базу проекта "Armwrestling Power Arena" и подготовь детальный отчет. Действуй строго по пунктам ниже.

### **1. Обзор высокого уровня: Архитектура и Потоки Данных**

0.  **Документация проекта**:
    *   Изучи все файлы в папке `docs/`. Оцени насколько документация полная, актуальна и соответствует текущему состоянию проекта.
    *   Изучи все правила проекта в папке `docs/rules/`. Во время анализа кодовой базы, обрати внимание на то, чтобы правила соблюдались.

1.  **Диаграмма Архитектуры**: Создай текстовую или Mermaid-диаграмму, иллюстрирующую ключевые компоненты системы:
    *   Frontend (Next.js App Router)
    *   Backend as a Service (Supabase Auth, Supabase DB)
    *   Взаимодействие между ними (Server Actions, Route Handlers, прямой вызов Supabase client).
2.  **Описание структуры репозитория**: Опиши назначение всех директорий в проекте (`/app`, `/components`, `/libs`, `/hooks`, `/styles` и т.д.). Какие принципы лежат в основе текущей структуры?
3.  **Основные потоки данных (Data Flow)**: Проанализируй и опиши 3-4 ключевых сценария использования приложения. Например:
    *   **Аутентификация пользователя**: Как происходит регистрация, вход и управление сессией? Проанализируй механизм управления сессией: используются ли `cookies` (например, через `@supabase/ssr` или middleware в Next.js для Server Components/Actions) или `localStorage` (стандартное поведение Supabase JS client)? Насколько этот подход консистентен во всем приложении?
    *   **Получение и отображение данных**: Как страницы получают данные? Проанализируй использование Server Components для fetching'а данных, клиентских компонентов с `useEffect`/SWR/React Query, и Route Handlers.
    *   **Изменение данных**: Как происходит создание/обновление/удаление данных (например, через Server Actions или API-запросы к Route Handlers)? Как обновляется UI после этого (использование `revalidatePath`, `router.refresh()` и т.д.)?
4.  **Детализированная диаграмма связей файлов (File Interaction Diagram)**:
    *   Выбери все ключевые фичи.
    *   Для каждой фичи создай отдельную Mermaid-диаграмму (`graph TD`), которая показывает цепочку взаимодействия файлов: от компонента в директории `/app` -> к дочерним компонентам -> к хукам -> к утилитам в `/libs` -> и к финальному вызову Supabase.
    *   **Цель**: Визуализировать зависимости, найти слишком сложные цепочки вызовов и выявить изолированные/неиспользуемые файлы.

### **2. Глубокий анализ качества кода и следования принципам**

1.  **Принципы программирования**:
    *   **DRY (Don't Repeat Yourself)**: Найди дублирующиеся компоненты, функции, стили Tailwind CSS или целые логические блоки. Предложи конкретные способы рефакторинга в переиспользуемые утилиты, кастомные хуки или общие компоненты.
    *   **KISS (Keep It Simple, Stupid)**: Есть ли в коде избыточно усложненные решения, которые можно упростить? Приведи примеры функций или компонентов, которые можно переписать более лаконично и понятно.
    *   **SOLID**:
        *   **Single Responsibility Principle (SRP)**: Оцени, насколько компоненты и функции соответствуют этому принципу. Найди "божественные компоненты" (God Components) или "божественные хуки", которые берут на себя слишком много ответственности, и предложи, как их можно декомпозировать.
2.  **Антипаттерны и "Code Smells"**:
    *   **Prop Drilling**: Найди места, где пропсы "пробрасываются" через множество уровней компонентов. Предложи использование `React Context` или композиции компонентов.
    *   **Неправильное разграничение Client/Server Components**: Есть ли компоненты, помеченные как `"use client"`, без необходимости (например, они не используют хуки или интерактивность)? Или наоборот, есть ли попытки использовать хуки в серверных компонентах?
    *   **Злоупотребление `useEffect`**: Проверь хуки `useEffect` на предмет правильного массива зависимостей, отсутствия вечных циклов и потенциальных race conditions в клиентских компонентах.
3.  **Анализ системы линтинга и форматирования**:
    *   Проверь наличие и содержимое файлов конфигурации (похоже что biome и eslint конфликтуют, но biome предпочтительней).
    *   Проанализируй скрипты в `package.json` (например, `"lint"`, `"format"`). Настроен ли в проекте автоматический линтинг и форматирование?
    *   Оцени, насколько конфигурация линтера полная. Используются ли стандартные правила (`next/core-web-vitals`) или есть кастомные правила, плагины (например, для Tailwind CSS, import sorting)?

### **3. Анализ стека и зависимостей**

1.  **Next.js (App Router)**:
    *   Проанализируй использование возможностей App Router: Server Components, Client Components, Server Actions, Route Handlers. Применяются ли они корректно и к месту?
    *   Оцени стратегию кэширования. Правильно ли используются опции `fetch` ( `cache`, `next: { revalidate }` ) и функции `revalidatePath`/`revalidateTag`?
2.  **Supabase**:
    *   **Безопасность**: Проверь, как используются ключи Supabase. Не утекает ли `service_role key` на клиент? Критически важно проверить, настроены ли политики **Row Level Security (RLS)** на таблицах, и покрывают ли они все сценарии доступа к данным.
    *   **Эффективность запросов**: Проанализируй запросы к Supabase. Используются ли `.select()` для получения только нужных колонок? Есть ли N+1 запросы, особенно в серверных компонентах, рендерящих списки?
3.  **Tailwind CSS**:
    *   Оцени консистентность использования. Есть ли самописные классы, которые дублируют утилиты Tailwind? Есть ли злоупотребление директивой `@apply` в CSS-файлах, что противоречит философии Tailwind?

### **4. Глубокий Аудит Безопасности**

При анализе кода ищи следующие распространенные проблемы безопасности:
*   **Уязвимости валидации ввода**
*   **Слабая аутентификация или авторизация**
*   **Небезопасная обработка чувствительных данных**
*   **Инъекции (SQL-инъекции, XXE, инъекции команд и т.д.)**
*   **Межсайтовый скриптинг (XSS)**
*   **Небезопасные настройки конфигурации**
*   **Устаревшие или уязвимые зависимости**
*   **Повышение привилегий**
*   **Неограниченное потребление ресурсов (через DoS и т.д.)**
*   **Небезопасная криптография (например, слабые ключи)**
*   **Неограниченная загрузка файлов**
*   **Небезопасная десериализация**
*   **Небезопасная генерация случайных чисел**
*   **Небезопасное логирование и мониторинг**
*   **Атаки десериализации (например, Pickle)**
*   **Уязвимости бизнес-логики (пример сценария: пользователь может вывести средства 3 раза подряд, но код допускает 4)**

Для каждой найденной уязвимости предоставь:
1.  Путь к файлу и номер(а) строки(строк).
2.  Описание проблемы и почему это является уязвимостью.
3.  Потенциальное воздействие, если уязвимость будет проэксплуатирована.
4.  Фрагменты кода, ответственные за уязвимость, от источника (source) до приемника (sink), и укажи, какой пользовательский ввод или значение передается.
5.  PoC (Proof of Concept) для эксплуатации.
6.  Рекомендации по исправлению или смягчению уязвимости.

После завершения анализа кодовой базы предоставь Markdown-таблицу со следующими заголовками: `Название уязвимости`, `Описание уязвимости`, `Путь к файлу`, `Вектор CVSS`, `Уверенность`, `Шаги для эксплуатации`.

Будь максимально тщательным и подробным в своем анализе. Безопасность этой кодовой базы имеет решающее значение.

### **5. Анализ Производительности**

1.  **Размер клиентского бандла**: Выдели компоненты, которые являются главными кандидатами на увеличение размера клиентского бандла. Предложи использование `next/dynamic` для тяжелых клиентских библиотек или компонентов, которые не нужны при первой загрузке.
2.  **Оптимизация изображений**: Используется ли компонент `next/image` для всех изображений?
3.  **Мемоизация и ре-рендеры**: Оцени использование `React.memo`, `useCallback`, `useMemo` в клиентских компонентах. Есть ли места, где их не хватает, что приводит к лишним ре-рендерам, или наоборот, где они используются избыточно?

### **6. Итоговый План Улучшений (Action Plan)**

Создай финальный раздел в виде приоритизированного списка действий.

*   **Приоритет: Высокий (Критично)**: Проблемы, напрямую влияющие на **безопасность** (все найденные уязвимости), вызывающие серьезные баги или ведущие к падению приложения.
*   **Приоритет: Средний (Рекомендуется)**: Проблемы, которые сильно ухудшают поддерживаемость, производительность и масштабируемость (например, крупный рефакторинг, внедрение стейт-менеджера для устранения prop drilling, перевод SSR-страниц на SSG, где это применимо).
*   **Приоритет: Низкий (Желательно)**: Косметические улучшения, мелкий рефакторинг, улучшение консистентности кода.

Для каждого пункта в плане предоставь:
1.  **Проблема**: Краткое описание проблемы.
2.  **Расположение**: Пути к файлам, где проблема обнаружена.
3.  **Решение**: Конкретное предложение по исправлению.

Представь итоговый отчет в структурированном, легко читаемом формате (Markdown).
