# Admin Documentation

This document outlines features and workflows specific to application administrators.

## Exercise Management

Administrators can manage the master list of exercises available in the application via the `/admin/exercises` page.

### Workflow

1.  **Accessing:** Navigate to `/admin/exercises` while logged in as an admin.
2.  **Viewing:** The page displays a table of all existing exercises, showing their title and creation date.
3.  **Creating:**
    *   Click the "Create New Exercise" button.
    *   A modal form will appear.
    *   Fill in the required details:
        *   **Title:** The name of the exercise (e.g., "Pronation Lift", "Cup Progression").
        *   **Description:** How to perform the exercise.
        *   **Video Tutorial URL (Optional):** A valid URL (e.g., `https://...`) pointing to a video demonstration.
        *   **Equipment Required (Optional):** A comma-separated list of equipment (e.g., "Loading Pin, Strap, Handle").
        *   **Evaluation Criteria:** How performance is measured (e.g., "Max weight lifted (kg)", "Max reps in 60s").
        *   **Medal Thresholds (JSON):** Define the performance levels required for bronze, silver, and gold medals in valid JSON format. The keys must be `bronze`, `silver`, and `gold`. Example: `{ "bronze": 50, "silver": 75, "gold": 100 }`
    *   Click "Save Exercise". The exercise will be added, and the list will refresh.
4.  **Editing:**
    *   Click the "Edit" button on the row of the exercise you wish to modify.
    *   The modal form will appear, pre-filled with the existing data.
    *   Update the necessary fields.
    *   Click "Save Exercise". The changes will be saved, and the list will refresh.
5.  **Deleting:**
    *   Click the "Delete" button on the row of the exercise you wish to remove.
    *   A confirmation dialog will appear.
    *   Confirm the deletion by clicking "Delete" (or "Continue"). The exercise will be permanently removed, and the list will refresh.

### Important Notes

*   Only users with the 'admin' role can access this page and perform these actions.
*   The `medal_thresholds` field requires strictly valid JSON input.
*   Server-side validation ensures data integrity, but filling fields correctly according to prompts is recommended. 